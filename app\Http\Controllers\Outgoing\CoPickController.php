<?php

namespace App\Http\Controllers\Outgoing;

use App\Services\PickShipService;
use App\StageLoc;
use App\UomConv;
use Illuminate\Http\Request;
use App\Item;
use App\Http\Controllers\Controller;
use App\Services\GeneralService;
use App\Services\POService;
use App\View\CoPickView;
use App\ItemLoc;
use App\ItemWarehouse;
use App\Loc;
use App\CustomerOrderItem;
use App\CustomerOrder;
use App\StagingLine;
use Alert;
use App\Http\Controllers\BarcodeController;
use App\Services\LotService;
use DB;
use App\View\TparmView;
use Carbon\Carbon;
use App\AlternateBarcode;
use App\Container;
use App\ContainerItem;
use App\Services\PalletService;
use App\Services\UOMService;
use Exception;
use Illuminate\Support\Facades\Session;
use Illuminate\Validation\ValidationException;

use App\Services\SapCallService;
use App\MatlTrans;
use App\LotLoc;
use App\Services\ItemLocService;
use Illuminate\Support\Facades\Cache;
use App\Http\Controllers\ApiController;
use App\Services\CatchWeightService;
use App\Services\CoPickService;
use App\SiteSetting;
use Timezone;
use App\Http\Controllers\ValidationController;
use App\Services\SapApiCallService;


class  CoPickController extends Controller
{
    use \App\Traits\HasDefaultLoc;
    private $pickShipService;

    public function __construct(PickShipService $pickShipService)
    {
        $this->pickShipService = $pickShipService;
    }

    public function index()
    {

        if (!\Gate::allows('hasCoPick')) {
            return view('errors.404')->with('page', 'error');;
        }

        $tparm = new TparmView();
        $loc_num = null;


        if (auth()->user()->getCurrWhse() != null) {
            // Get default packing location of this warehouse
            $default_packing_location = $tparm->getTparmValueDefLocation('CustOrdPicking', auth()->user()->getCurrWhse());

            if ($default_packing_location) {
                $loc_num = $default_packing_location->loc_num;
            }
        }

        $def_pick_method = $tparm->getTparmValue('CustOrdPicking', 'def_pick_method');
        $tparm = $tparm->getTparmValue('CustOrdPicking', 'enable_warehouse');

        $lpnDef = PalletService::getDefaultLpnTransaction('CO Picking');
        // dd(session()->get('select_pick_by'));
        if ($def_pick_method) {
            $def_ship_by = $def_pick_method;
        } else {
            if (session()->get('select_pick_by') == "CO" || empty(session()->get('select_pick_by'))) {
                $def_ship_by =  "Customer Order";
            } else {
                $def_ship_by =  "Customer";
            }
        }




        //dd($def_ship_by, session());
        // dd('ddddwwsss');
        return view('shipping.copick.index', compact('loc_num', 'tparm', 'lpnDef', 'def_ship_by'));
    }

    public function changeStageLoc($whse_num = "")
    {
        $tparm = new TparmView();
        $def_location = $tparm->getTparmValue('CustOrdPicking', 'def_location');

        if ($def_location) {
            $def_location = $tparm->getTparmValueDefLocation('CustOrdPicking', $whse_num);

            if ($def_location != null) {
                $defaults['loc_num'] = $def_location->loc_num;
                return $defaults['loc_num'];
            }
        } else {
            return "";
        }
    }

    public function CoPickingDetails(Request $request)
    {
        // dd($request->all());

        $shipping_zone_code = request('shipping_zone_code');
        $strsales_person    = request('strsales_person');
        $whse_numss = request('whse_num');
        $co_num_base64 = request('co_num_base64');
        $select_pick_by     = $request->select_pick_by;
        $pick_by     = $request->pick_by;
        $item_num           = $request->item_num;
        $cust_num           = $request->cust_num;
        $stage_num          = request('stage_num');
        session()->put('select_pick_by', $select_pick_by);
        session()->put('pick_by_as', $pick_by);
        session()->put('shipping_zone_code', $shipping_zone_code);
        session()->put('strsales_person', $strsales_person);
        session()->put('item_num', $item_num);
        session()->put('cust_num', $cust_num);
        session()->put('whse_num', $whse_numss);
        session()->put('co_num_base64', $co_num_base64);
        session()->put('stage_num', $stage_num);
        //dd($pick_by, $select_pick_by, $request);
        //dd(session());
        // dd($item_num, $shipping_zone_code, $strsales_person, $select_pick_by);
        // dd($request, $cust_num);
        if ($select_pick_by == "Customer") {
            // Check the CO Header and The co_num
            $getCheckCoNum = CustomerOrder::where('co_num', $request->co_num)->where('cust_num', $request->cust_num)->where('co_status', 'O')->exists();
            $query = DB::query()->from('customer_orders as co')
                ->select('co.co_num')
                //->leftJoin('customers','customers.cust_num','=','co.cust_num')
                ->where('co.site_id', auth()->user()->site_id)
                ->where('co.co_status', 'O')
                ->where('co.cust_num', request('cust_num'));

            if (request('shipping_zone_code')) {

                $query->where('co.shipping_zone_code', request('shipping_zone_code'));
            }

            if (request('strsales_person')) {
                $query->where('co.strsales_person', request('strsales_person'));
            }
            $final =   $query->get()->toArray();

            $arrFinalCoNum = array();
            $arrPush = array();
            $arrPushNew = array();

            //dd($final, $request->pick_by);
            // http://127.0.0.1:8000/home/<USER>/co-picking/list?_token=VJIUSaXoD9mB2JzUg1P8elGmLdhDidkKRanZLQL1&pick_by=pallet&select_pick_by=Customer&whse_num=Testwhse01&co_num=&co_line=&cust_num=Customer01&cust_name=Customer01&stage_num=Picking+loc&item_num=&shipping_zone_code=&strsales_person=
            // $checkCoDetails[] = 0;
            //
            $countFinal = count($final ?? []);
            $ind = 0;
            //dd($final);
            foreach ($final as $key) {
                // echo $key->co_num;
                // echo "<br>";

                if ($request->pick_by == "unit" || $request->pick_by == null) {
                    $checkCoDetails[$key->co_num] =  DB::table('stage_locs')
                        ->whereNotNull('lpn_num')
                        ->where('co_num', $key->co_num)
                        ->where('site_id', auth()->user()->site_id)
                        ->count();
                    $strMsg = __('error.mobile.pick_pallet_error');
                } else {
                    $checkCoDetails[$key->co_num] =  DB::table('stage_locs')
                        ->whereNull('lpn_num')
                        ->where('co_num', $key->co_num)
                        ->where('site_id', auth()->user()->site_id)
                        ->count();
                    $strMsg = __('error.mobile.pick_unit_error');

                    // dd($strMsg, $checkCoDetails[$key->co_num], $final);
                    //$checkCoDetails[$key->co_num] = 0;
                }
                //dd($checkCoDetails[$key->co_num],$strMsg);
                if ($checkCoDetails[$key->co_num] > 0) {
                    // Incompatible picking method. Order has unit-picked lines. Choose 'Pick By Unit' for the entire order.
                    // dd('sxsxsxs',$key->co_num);
                    if ($countFinal == 1) {
                        Alert::error('Error', $strMsg);
                        return back();
                    }
                    // $arrPush['error_co_num'][$ind] = @$key->co_num;
                } else {
                    //$arrPush['co_num'][$ind] =@$key->co_num;
                    // array_push($arrPushNew, $key->co_num);
                    array_push($arrPush, $key->co_num);
                }
                $ind++;
            }

            // 3 => "Mot1450"
            // 5 => "Mot1452"
            // 9 => "Mot6198"
            //dd($arrPush,$arrPushNew);
            if (!\Gate::allows('hasCoPick')) {
                return view('errors.404')->with('page', 'error');;
            }
            $request = validateSansentiveValue($request);
            $tparm = new TparmView;
            $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting');
            $request->validate([
                'whse_num' => 'required|exists:warehouses,whse_num,whse_status,1,site_id,' . auth()->user()->site_id,
            ], [
                'whse_num.exists' =>  __('error.mobile.processinactive', ['resource' => __('mobile.list.warehouses')]),
            ]);

            $arrStoreCoNum = [];
            // dd($final,$arrPush);
            // Send error if co_num's status is not open
            if (count($arrPush ?? []) > 0) {
                foreach ($arrPush as $key => $co_num) {
                    // dd($co_num);

                    //$arrStoreCoNum[$key->co_num] = $key->co_num;
                    $checkCoNum = CustomerOrderItem::where('co_num', $co_num)->where('whse_num', $request->whse_num)->where('rel_status', '!=', "C");

                    if ($item_num) {
                        $checkCoNum->where('item_num', $item_num);
                    }
                    $checkCoNum = $checkCoNum->exists();

                    //dd($checkCoNum);

                    // Just store those co not Closed/Completed
                    if ($checkCoNum) {
                        array_push($arrStoreCoNum, $co_num);
                        // throw ValidationException::withMessages(['co_num' => 'CO-' . $key->co_num . ' cannot be proceed due to status is completed/closed']);
                    }


                    $getCustNum[$co_num] = CustomerOrderItem::where('co_num', $co_num)->where('whse_num', $request->whse_num)->first();
                }
            }
            // else{
            //     throw ValidationException::withMessages(['loc_num' => 'Stage Location-' . $request->stage_num . ' cannot be proceed due too customer num empty.']);
            // }
            //dd($arrStoreCoNum);
            // Send error if location is not active
            $checkStageLoc = Loc::where('whse_num', $request->whse_num)->where('loc_num', $request->stage_num)->first();
            if ($checkStageLoc) {
                if ($checkStageLoc->loc_status == 0) {
                    throw ValidationException::withMessages(['loc_num' => 'Stage Location-' . $request->stage_num . ' cannot be proceed due to status is inactive']);
                }

                if ($checkStageLoc->loc_type == 'T') {
                    throw ValidationException::withMessages(['loc_num' => 'Stage Location-' . $request->stage_num . ' cannot be proceed due to Location is a Transit Location']);
                }
            } else {
                throw ValidationException::withMessages(['loc_num' => 'Stage Location-' . $request->stage_num . ' not exist']);
            }


            $stage_num = $request->get('stage_num');
            if (is_null($stage_num)) {
                $stage_num =  session('stage_num');
            }
            $whse_num = $request->get('whse_num');
            $co_list = CoPickView::whereIn('co_num', $arrStoreCoNum)
                ->where('whse_num', $whse_num)
                ->select('co_num', 'co_line', 'co_rel', 'item_num', 'uom', 'item_desc', 'qty_released', 'qty_shipped', 'qty_returned', DB::raw("IFNULL(SUM(qty_staged),0) as qty_staged"))
                ->where('co_line', 'like', '%' . $request->co_line . '%')
                // ->where('item_num', $request->item_num)
                //->where('item_num', '!=','NON-INV')
                ->orderBy('co_num')
                //->groupBy('co_line')
                //->groupBy('co_rel')
                ->get();

            // dd($co_list,$request);



            if ($request->item_num && ($co_list->count() == 1)) {
                $coitem = $co_list->first();
                $request->merge([
                    'co_rel' => $coitem->co_rel,
                    'co_line' => $coitem->co_line,
                    'stage_num' => base64_encode(request('stage_num')),
                ]);

                $co_num = request('co_num') ?? base64_encode($coitem->co_num);
                $request['co_num'] = is_base64_string($co_num) ? $co_num : base64_encode($co_num);
                $request['item'] = base64_encode($request->item_num);
                $request['whse_num'] = base64_encode($request->whse_num);
                $request->merge([
                    'uom' => base64_encode($coitem->uom)
                ]);

                if ($coitem->co_num === null) {
                    throw ValidationException::withMessages(['item_num' => 'Item does not exist in selected Customer / CO']);
                }

                return redirect()->route('CoPickingProcess', $request->all(), 303);
            }


            // count
            if (count($arrStoreCoNum ?? []) > 1) {
                $arrStoreCoNum = implode(",", $arrStoreCoNum);
            } else {
                $arrStoreCoNum = $arrStoreCoNum[0] ?? null;
            }
            // dd($arrStoreCoNum);
            //dd($arrStoreCoNum);

            if ($request->pick_by == 'pallet') {
                return view('shipping.copick.newcopalletlist')->with('co_list', $co_list)->with('co_num', $arrStoreCoNum)->with('stage_num', $stage_num)->with('co_line', $request->co_line)->with('whse_num', $request->whse_num)->with('unit_quantity_format', $unit_quantity_format)->with('item_num', $request->item_num)->with('cust_num', $request->cust_num)->with('strsales_person', $request->strsales_person)->with('shipping_zone_code', $request->shipping_zone_code)->with('select_pick_by', $select_pick_by);
            }

            return view('shipping.copick.newcolistbycustomer')->with('co_num_arr', $arrStoreCoNum)->with('co_list', $co_list)->with('strsales_person', $request->strsales_person)->with('shipping_zone_code', $request->shipping_zone_code)->with('cust_num', $request->cust_num)->with('co_num', $request->co_num)->with('stage_num', $stage_num)->with('co_line', $request->co_line)->with('whse_num', $request->whse_num)->with('unit_quantity_format', $unit_quantity_format)->with('item_num', $request->item_num)->with('select_pick_by', $select_pick_by);
            //dd($final,$checkCoDetails,$request,$co_list);


        }


        // Check Validation for the stage_loc
        $checkCoDetails = 0;
        if ($request->co_num_base64 == 1) {
            $request->merge([
                'co_num' => base64_encode(request('co_num')),
            ]);
            $co_num = base64_decode($request->co_num);
        } else {

            $co_num = $request->co_num;
        }

        //dd($co_num, $request);
        // dd($request->all());
        if ($request->pick_by == "unit" || $request->pick_by == null) {
            $checkCoDetails =  DB::table('stage_locs')
                ->whereNotNull('lpn_num')
                ->where('co_num', $co_num)
                ->where('site_id', auth()->user()->site_id)
                ->count();
            $strMsg = __('error.mobile.pick_pallet_error');
        } else {
            $checkCoDetails =  DB::table('stage_locs')
                ->whereNull('lpn_num')
                ->where('co_num', $co_num)
                ->where('site_id', auth()->user()->site_id)
                ->count();
            $strMsg = __('error.mobile.pick_unit_error');
        }


        if ($checkCoDetails > 0) {
            // Incompatible picking method. Order has unit-picked lines. Choose 'Pick By Unit' for the entire order.

            Alert::error('Error', $strMsg);


            return back();
        }




        if (!\Gate::allows('hasCoPick')) {
            return view('errors.404')->with('page', 'error');;
        }
        $request = validateSansentiveValue($request);
        $tparm = new TparmView;
        $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting');
        $request->validate([
            'whse_num' => 'required|exists:warehouses,whse_num,whse_status,1,site_id,' . auth()->user()->site_id,
        ], [
            'whse_num.exists' =>  __('error.mobile.processinactive', ['resource' => __('mobile.list.warehouses')]),
        ]);

        // Send error if co_num's status is not open

        $checkCoNum = CustomerOrderItem::where('co_num', $co_num)->where('whse_num', $request->whse_num)->where('rel_status', '!=', "C")->exists();
        $checkWhse = CustomerOrderItem::where('co_num', $co_num)->where('rel_status', '!=', "C")->first();
        // dd($checkCoNum, $co_num, $request->whse_num, $checkWhse);
        if (!$checkCoNum && $checkWhse) {
            //dd($request,'sssjedjj');
            if ($checkWhse->whse_num != $request->whse_num) {
                //throw ValidationException::withMessages(['co_num' => 'CO-' . $co_num. ' not matching with ' . $request->whse_num]);
                throw ValidationException::withMessages(['co_num' => 'CO [' . $co_num . '] does not match Whse [' . $request->whse_num . ']']);
            } else {
                // throw ValidationException::withMessages(['co_num' => 'CO-' . $co_num . ' cannot be proceed due to status is completed/closed']);
                throw ValidationException::withMessages(['co_num' => 'CO-' . __('error.mobile.status_is_completed', ['resource' => $co_num])]);
            }
        }
        // // Check CO Status
        // $checkCoNumStatus = CustomerOrderItem::where('co_num', $co_num)->where('whse_num', $request->whse_num)->first();
        // if ($checkCoNumStatus && $checkCoNumStatus->rel_status == 'C') {
        //     throw ValidationException::withMessages(['co_num' => 'CO-' . __('error.mobile.status_is_completed', ['resource' => $co_num])]);
        // }

        // Send error if location is not active
        $checkStageLoc = Loc::where('whse_num', $request->whse_num)->where('loc_num', $request->stage_num)->first();
        if ($checkStageLoc) {
            if ($checkStageLoc->loc_status == 0) {
                throw ValidationException::withMessages(['loc_num' => 'Stage Location-' . $request->stage_num . ' cannot be proceed due to status is inactive']);
            }

            if ($checkStageLoc->loc_type == 'T') {
                throw ValidationException::withMessages(['loc_num' => 'Stage Location-' . $request->stage_num . ' cannot be proceed due to Location is a Transit Location']);
            }
        } else {
            throw ValidationException::withMessages(['loc_num' => 'Stage Location-' . $request->stage_num . ' not exist']);
        }

        $getCustNum = CustomerOrderItem::where('co_num', $co_num)->where('whse_num', $request->whse_num)->first();

        // $co_num = $request->get('co_num');
        $whse_num = $request->get('whse_num');
        $co_line = $request->get('co_line');
        $stage_num = $request->get('stage_num');
        if (is_null($stage_num)) {
            $stage_num =  session('stage_num');
        }



        // dd($co_num, $checkSalesperson, $request);


        $co_list = CoPickView::where('co_num', $co_num)
            ->where('whse_num', $whse_num)
            ->select('co_num', 'co_line', 'co_rel', 'uom', 'item_num', 'item_desc', 'qty_released', 'qty_shipped', 'qty_returned', DB::raw("IFNULL(SUM(qty_staged),0) as qty_staged"))

            //->where('item_num', $request->item_num)
            //->where('item_num', '!=','NON-INV')
            ->orderBy('co_num')
            ->groupBy('co_line')
            ->groupBy('co_rel');


        if ($request->item_num && $request->item_num != "NON-INV") {
            $co_list = $co_list->where('item_num', $request->item_num);
        }
        if ($request->co_line) {
            $co_list = $co_list->where('co_line', 'like', '%' . $request->co_line . '%');
        }


        $co_list = $co_list->get();





        if ($request->item_num && ($co_list->count() == 1)) {
            // Single line and item selected, redirect to picking process
            $coitem = $co_list->first();
            $request['co_rel'] = $coitem->co_rel;
            $request['co_line'] = $coitem->co_line;
            $request['co_num'] = is_base64_string($request->co_num) ? $request->co_num : base64_encode($request->co_num);
            $request['stage_num'] = base64_encode($request->stage_num);
            $request['item'] = base64_encode($request->item_num);
            $request['whse_num'] = base64_encode($request->whse_num);
            $request->merge([
                'uom' => base64_encode($coitem->uom)
            ]);

            return redirect()->route('CoPickingProcess', $request->all(), 303);
        }

        if ($request->pick_by == 'pallet') {
            return view('shipping.copick.newcopalletlist')->with('co_list', $co_list)->with('co_num', $co_num)->with('stage_num', $stage_num)->with('co_line', $request->co_line)->with('whse_num', $request->whse_num)->with('unit_quantity_format', $unit_quantity_format)->with('item_num', $request->item_num)->with('cust_num', $getCustNum->cust_num)->with('strsales_person', $request->strsales_person)->with('shipping_zone_code', $request->shipping_zone_code)->with('select_pick_by', $select_pick_by);
        }
        return view('shipping.copick.newcolist')->with('co_list', $co_list)->with('co_num', $co_num)->with('stage_num', $stage_num)->with('co_line', $request->co_line)->with('whse_num', $request->whse_num)->with('unit_quantity_format', $unit_quantity_format)->with('item_num', $request->item_num)->with('select_pick_by', $select_pick_by)->with('strsales_person', $request->strsales_person)->with('shipping_zone_code', $request->shipping_zone_code)->with('totalrecord', $co_list->count());
    }

    public function CoPickingProcess(Request $request)
    {

        if (!\Gate::allows('hasCoPick')) {
            return view('errors.404')->with('page', 'error');;
        }
        $tparm = new TparmView;
        $total_quantity_format = $tparm->getTparmValue('System', 'decimal_setting_total_qty');
        $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting');
        $stage_num = base64_decode($request->stage_num);
        $whse_num = base64_decode($request->whse_num);
        $co_num = base64_decode($request->co_num);
        $item_num = base64_decode($request->item);
        $uom = base64_decode($request->uom);
        $batch_id = generateBatchId("CustOrdPicking");

        $tparmallowovership = new TparmView;
        $intAllowOverPick  = $tparmallowovership->getTparmValue('CustOrdShipping', 'allow_over_ship');


        // Check the NON-INV 1=NON_INV / Child  0=Normal
        //  $getCheckNONINV =  DB::table('coitems_sap_exts')->select('id')->where(
        //     [
        //         ['co_num', '=',  $co_num ],
        //         ['co_line', '=', $request->co_line],
        //         ['site_id', '=', auth()->user()->site_id]
        //     ]
        //     )->whereNotNull('family_line')->exists();
        //dd($item_num,$request,base64_decode($request->item));
        if ($item_num == "NON-INV") {
            $getCheckNONINV = 1;
        } else {
            $getCheckNONINV = 0;
        }
        //dd($getCheckNONINV, $item_num);

        $disable_lot_number_selection = 0;
        $defaults = [
            'lot_num' => null,
            'loc_num' => null,
            'qty_available' => null,
            'qty_available_conv' => null,
            'base_uom' => null,
            'uom_conv' => null,
        ];

        $qty_staged = CoPickView::where('co_num', $co_num)
            ->where('co_line', $request->co_line)
            ->where('co_rel', $request->co_rel)
            ->groupBy('co_line')
            ->sum('qty_staged');

        $co_item = new CoPickView();
        $co_item = $co_item->with('item')
            ->where('co_num', $co_num)
            ->where('co_line', $request->co_line)
            ->where('co_rel', $request->co_rel)
            ->first();
        //dd($co_item,$request);
        if ($co_item) {
            $tparm = new TparmView;
            $def_location = $tparm->getTparmValueDefLocation('CustOrdPicking', $co_item->whse_num);
            if ($def_location) {
                $defaults['loc_num'] = $def_location->loc_num;
                $def_loc = $def_location->loc_num;

                if ($stage_num == $def_loc) {
                    $def_loc = "";
                }


                //$def_loc = "";
            } else {
                $defaults['loc_num'] = null;
                $def_loc = "";
            }
            // dd($def_location);
            @$defaults = $this->getDefaultIssueLocPL($co_item->whse_num, $co_item->item_num, $co_item->item->lot_tracked, $defaults['loc_num']);

            $default_picking_location = $tparm->getTparmValueDefLoc2('CustOrdPicking', 'def_picking_location', $co_item->whse_num);
            if ($default_picking_location) {
                $default_picking_location = $default_picking_location->loc_num;
                @$defaults = $this->getDefaultIssueLocPL($co_item->whse_num, $co_item->item_num, $co_item->item->lot_tracked,  $default_picking_location);
            }

            if (@$co_item->item->lot_tracked == 1 || $getCheckNONINV == 0) {
                // Get tparm's disable_lot_number_selection value
                $tparm = new TparmView;
                $disable_lot_number_selection = $tparm->getTparmValue('CustOrdPicking', 'disable_lot_number_selection');

                // $lotloc = $this->getDefaultLocLotQtyELNS($co_item,$co_item->item,$disable_lot_number_selection,$defaults['loc_num']);
                $lotloc = $this->getDefaultLocLotQtyELNSPL($co_item, $co_item->item, $disable_lot_number_selection);
                if ($lotloc) {
                    $defaults['lot_num'] = $lotloc->lot_num;
                    $defaults['loc_num'] = $lotloc->loc_num;
                    $defaults['uom_conv'] = $lotloc->uom;
                    $defaults['base_uom'] = $lotloc->uom;
                    $defaults['qty_available_conv'] = $lotloc->qty_available;
                    $defaults['qty_available'] = $lotloc->qty_available;
                } else {
                    // dd($defaults);
                    // $defaults['lot_num'] = null;
                    // $defaults['loc_num'] = null;
                    // $defaults['uom_conv'] = null;
                    // $defaults['qty_available_conv'] = null;
                    // $defaults['qty_available'] = null;
                }
            }
            // dd($defaults, "lklklk");
            if ($getCheckNONINV == 0 && @$co_item->item->uom != $co_item->uom && $defaults['qty_available']) {
                $conv = UomConv::convert($co_item->item->uom, $defaults['qty_available'], $co_item->item->item_num, $co_item->cust_num, '', $co_item->uom);
                $defaults['uom_conv'] = $conv['uom'];
                $defaults['qty_available_conv'] = $conv['qty'];
            }
        } else {
            Alert::error(__('error.admin.error_general'))->persistent('Dismiss');
            return redirect()->back();
        }


        if (@$defaults['base_uom'] == "" && $getCheckNONINV == 0) {
            @$defaults['base_uom'] = Item::where('item_num', $co_item->item_num)->value('uom');
        } else {

            @$defaults['base_uom'] = $uom;
            @$defaults['uom_conv'] = $uom;
        }

        // dd($request,$whse_num);

        //  $input['co_num_base64'] = @$request->co_num_base64 ?? 0;
        $input['all'] = $request->all();
        $input['count_list'] = $request->totalrecord;
        $input['indicate'] = 1;
        $url = generateRedirectUrl('CoPick', $input);

        // Check if item is catch weight enabled and redirect to catch weight view
        $tparm = new TparmView;
        $printLabel = $tparm->getTparmValue('COPick', 'print_label');
        $allow_over_pick = $intAllowOverPick;

        $view = $co_item->item->catch_weight ? 'shipping.copick.process_cw' : 'shipping.copick.process';

        //dd($input);
        return view($view)
            ->with('co_item', $co_item)
            ->with('stage_num', $stage_num)
            ->with('qty_staged', $qty_staged)
            ->with('defaults', @$defaults)
            ->with('disable_lot_number_selection', $disable_lot_number_selection)
            ->with('total_quantity_format', $total_quantity_format)
            ->with('unit_quantity_format', $unit_quantity_format)
            ->with('def_loc', @$def_loc)
            ->with('getCheckNONINV', $getCheckNONINV)
            ->with('default_picking_location', @$default_picking_location)
            ->with('url', $url->getTargetUrl())
            ->with('intAllowOverPick', $intAllowOverPick)
            ->with('allow_over_pick', $allow_over_pick)
            ->with('printLabel', $printLabel)
            ->with('batch_id', $batch_id);
    }

    public function CoPickList(Request $request)
    {
        //dd(request('select_pick_by'), request('pick_by'));
        //  dd($request->delivery_date);
        $tparm = new TparmView;
        $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting');

        $checkTparm = new TparmView;
        $check_allow_overship = $checkTparm->getTparmValue('CustOrdShipping', 'allow_over_ship');
        // dd($check_allow_overship);
        if ($request->ajax()) {
            $output = '';
            $query = $request->get('query');
            $co_num = utf8_encode(htmlspecialchars_decode(base64_decode($request->get('co_num'))));
            $whse_num = utf8_encode(htmlspecialchars_decode(base64_decode($request->get('whse_num'))));
            $co_line = $request->get('co_line');
            $stage_num = utf8_encode(htmlspecialchars_decode(base64_decode($request->get('stage_num'))));
            $item_num = utf8_encode(htmlspecialchars_decode(base64_decode($request->get('item_num'))));

            $strsales_person = utf8_encode(htmlspecialchars_decode(base64_decode($request->get('strsales_person'))));
            $select_pick_by = utf8_encode(htmlspecialchars_decode(base64_decode($request->get('select_pick_by'))));
            $cust_num = utf8_encode(htmlspecialchars_decode(base64_decode($request->get('cust_num'))));
            $shipping_zone_code = utf8_encode(htmlspecialchars_decode(base64_decode($request->get('shipping_zone_code'))));
            $co_num_arr  = utf8_encode(htmlspecialchars_decode(base64_decode($request->get('co_num_arr'))));
            $sort_by  = utf8_encode(htmlspecialchars_decode(base64_decode($request->get('sort_by'))));


            // session()->put('select_pick_by', $select_pick_by);

            //dd($select_pick_by);
            if ($query != '') {
                $coitem = new CustomerOrderItem();
                $data = $coitem->coItemSearchListNew($co_num, $whse_num, $co_line, $item_num, $query, $sort_by);
            } else {
                if ($select_pick_by == "Customer") {

                    $arrCoNum = explode(",", $co_num_arr);
                    $data = CoPickView::whereIn('co_num', $arrCoNum)
                        ->where('whse_num', $whse_num)
                        ->where('rel_status', '!=', 'C')
                        ->where('item_num', 'like', '%' . $item_num . '%');

                    if ($sort_by == 'co_line') {
                        $data = $data->orderBy('co_num', 'asc')->orderByRaw('cast(co_line as unsigned) ASC');
                    }
                    if ($sort_by == 'item_num') {
                        $data = $data->orderBy('item_num', 'asc');
                    }

                    $data = $data->get();
                } else {

                    // Checking the salesperson and shipping zode
                    $checkSalesperson = CustomerOrder::where('co_num', $co_num)->where('site_id', auth()->user()->site_id);

                    // dd($strsales_person, $item_num, $whse_num, $co_num, $co_line);
                    if ($strsales_person) {
                        $checkSalesperson->where('strsales_person', $strsales_person);
                    }
                    if ($shipping_zone_code) {
                        $checkSalesperson->where('shipping_zone_code', $shipping_zone_code);
                    }
                    $checkFinal = $checkSalesperson->exists();
                    if (!$checkFinal) {
                        $co_num = null;
                    }
                    $data = CoPickView::where('co_num', $co_num)
                        ->where('whse_num', $whse_num)
                        ->where('rel_status', '!=', 'C')
                        ->select('co_num',  'co_line', 'co_rel', 'item_num', 'item_desc', 'uom', 'qty_released', 'qty_shipped', 'qty_returned', 'qty_shortage', DB::raw("IFNULL(SUM(qty_staged),0) as qty_staged"))
                        ->where('co_line', 'like', '%' . $request->co_line . '%')
                        ->where('item_num', 'like', '%' . $item_num . '%');

                    if ($sort_by == 'co_line') {
                        $data = $data->orderByRaw('cast(co_line as unsigned) ASC');
                    }
                    if ($sort_by == 'item_num') {
                        $data = $data->orderBy('item_num', 'asc');
                    }

                    $data = $data->groupBy('co_line')->groupBy('co_rel')->get();
                }
            }


            // Reject if Qty Required is 0
            // Avoid the qty_shortage 0
            //if ($request->check != 1) {
            if ($check_allow_overship == 0) {
                $data = $data->reject(function ($data) {
                    return $data->qty_shortage <= 0;
                });
            }
            //}

            $total_row = $data->count();

            //dd($cust_num, $data, $total_row);

            if ($total_row > 0) {
                foreach ($data as $row) {
                    if ($select_pick_by == "Customer") {
                        $co_num_line = $row->co_num . "-" . $row->co_line;
                        $nameco = __('mobile.label.co_num');
                    } else {
                        $co_num_line = $row->co_line;
                        $nameco = __('mobile.label.co_line');
                    }
                    // dd(auth()->user()->site_id);
                    $getResuts = ItemWarehouse::where('item_num', $row->item_num)->where('whse_num', $whse_num)->where('site_id', auth()->user()->site_id)->first();
                    //dd($getResuts);
                    // echo @$getResuts->co_num;
                    // if (@$getResuts->qty_available == null) {
                    //     $qty_avail = ItemLocService::getTotalQtyAvailable($whse_num, $row->item_num);
                    //     // Update ItemWarehouse
                    //     $result =  ItemWarehouse::updateOrCreate(
                    //         [
                    //             'whse_num' => $whse_num,
                    //             'item_num' =>  $row->item_num,
                    //             'site_id'  => auth()->user()->site_id
                    //         ],
                    //         [
                    //             'qty_available' => $qty_avail

                    //         ]
                    //     );
                    // }
                    //dd($getResuts->uom);
                    $cust_num = CustomerOrder::select('cust_num')->where('co_num', $row->co_num)->where('co_status', 'O')->value('cust_num');
                    //dd($cust_num, $row->item_num);
                    if (@$getResuts->item_num != "") {


                        if ($row->uom != $getResuts->uom) {
                            // Do Convert Based on the Order Line
                            $qty_available =  UomConv::convertUOM($row->uom, $getResuts->uom, $getResuts->uom, $getResuts->qty_available, $row->item_num, $cust_num, '', __('mobile.nav.co_picking'));
                            //dd($qty_available,$row->uom,$getResuts->uom);

                            $qty_avail = $qty_available['conv_qty_to_base']['qty'];
                            $qty_avail_uom = $qty_available['conv_qty_to_base']['uom'] ?? null;
                        } else {
                            $qty_avail = $getResuts->qty_available;
                            $qty_avail_uom = $row->uom ?? null;
                        }
                    } else {
                        $qty_avail = 0;
                        $qty_avail_uom = $row->uom;
                        //dd($getResuts,$row-,$row->item_num,$whse_num,$row->uom,$item_num,$cust_num);
                    }




                    // if($row->item_num=="NON-INV"){
                    //     continue;
                    // }
                    $altBarCode = AlternateBarcode::where('item_num', $row->item_num)->where('site_id', auth()->user()->site_id)->get();
                    // if (is_null($row->qty_staged)) $row->qty_staged = 0;
                    // if ($row->qty_required - $row->qty_staged > 0) {
                    if ($request->check == 1 && $qty_avail >= 0) {
                        $output .= '
                            <form class="form" autocomplete="off" id="myform" method="GET" action="/home/<USER>/co-picking/process">
                                <div class="row border  border-primary" id="mybox" onclick="javascript:this.parentNode.submit();" >
                                <input type="hidden" name="_token" value="' . csrf_token() . '">
                                    <div class="col-xs-12">
                                        <table style="width: 100%;">
                                        <input type="hidden" value="' . $total_row . '" name="totalrecord">
                                         <input type="hidden" value="' . request('delivery_date', "") . '" name="delivery_date">
                                            <input type="hidden" value="' . request('delivery_trip', "") . '" name="delivery_trip">

                                            <input type="hidden" value="' . base64_encode($stage_num) . '" name="stage_num">
                                            <input type="hidden" value="' . base64_encode($row->co_num) . '" name="co_num">
                                            <tr>
                                                <td>
                                                    <label for="co_line"> ' . $nameco . '</label>
                                                </td>
                                                <td>
                                                    <input type="hidden" id="co_line" class="form-control border-primary" value="' . $row->co_line . '" readonly>
                                                    <span class="form-control border-primary pseudoinput">' . $co_num_line . ' </span>
                                                    <input type="hidden" name="co_line" class="form-control border-primary" value="' . $row->co_line . '">
                                                    <input type="hidden" name="co_rel" class="form-control border-primary" value="' . $row->co_rel . '">
                                                </td>
                                            </tr>
                                            <tr>
                                                <td><label>' . __('mobile.label.item_num') . '</label></td>
                                                <td colspan="4">';
                        foreach ($altBarCode as $barCode) {
                            $output .= '<span style="display: none"> ' . $barCode->alternate_barcode . ' </span>';
                        }
                        $output .= '
                                                    <span class="form-control border-primary pseudoinput">  ' . $row->item_num . ' </span>
                                                    <input readonly type="hidden" style="text-align:left;" name="item" class="form-control border-primary" value="' . base64_encode($row->item_num) . '">
                                                </td>
                                            </tr>
                                            <tr>
                                                <td width="70px"></td>
                                                <td colspan="4">
                                                <textarea readonly type="text" style="text-align:left;font-size:15px;" class="form-control border-primary">' . $row->item_desc . '</textarea>
                                                    <input name="item_desc" readonly type="hidden" style="text-align:left;"  class="form-control border-primary" value="' . $row->item_desc . '">
                                                </td>
                                            </tr>

                                           <tr>
                                            <td><label>' . __('mobile.label.qty_required') . '</label></td>
                                            <td>
                                               <span class="form-control border-primary pseudoinput" style="text-align:right;" >' . numberFormatPrecision($row->qty_shortage, $unit_quantity_format, '.', '') . ' </span>
                                                <input readonly size="10" type="hidden" class="form-control border-primary" name="qty_req" style="text-align:right;" value="' . numberFormatPrecision($row->qty_shortage, $unit_quantity_format, '.', '') . '">
                                            </td>
                                            <td>
                                                <span class="form-control border-primary pseudoinput">' . $row->uom . ' </span>
                                                <input type="hidden" class="form-control border-primary" name="uom" value="' . base64_encode($row->uom) . '" readonly>
                                            </td>
                                        </tr>


                                        <tr>
                                        <td><label>' . __('mobile.label.qty_available') . '</label></td>
                                        <td>
                                           <span class="form-control border-primary pseudoinput" style="text-align:right;" >' . numberFormatPrecision(@$qty_avail, $unit_quantity_format, '.', '') . ' </span>

                                        </td>
                                        <td>
                                            <span class="form-control border-primary pseudoinput">' . @$qty_avail_uom . ' </span>

                                        </td>
                                    </tr>
                                        </table>
                                    </div>
                                </div>
                            </form>
                        ';
                        // }
                    } else {
                        if ($request->check != 1 && $qty_avail > 0 || $row->item_num == "NON-INV") {
                            $output .= '
                    <form class="form" autocomplete="off" id="myform" method="GET" action="/home/<USER>/co-picking/process">
                        <div class="row border  border-primary" id="mybox" onclick="javascript:this.parentNode.submit();" >

                        <input type="hidden" name="_token" value="' . csrf_token() . '">
                            <div class="col-xs-12">
                                <table style="width: 100%;">
                                 <input type="hidden" value="' . $total_row . '" name="totalrecord">
                                    <input type="hidden" value="' . base64_encode($stage_num) . '" name="stage_num">
                                    <input type="hidden" value="' . base64_encode($row->co_num) . '" name="co_num">
                                          <input type="hidden" value="' . request('delivery_date', "") . '" name="delivery_date">
                                            <input type="hidden" value="' . request('delivery_trip', "") . '" name="delivery_trip">
                                    <tr>
                                        <td>
                                            <label for="co_line"> ' . $nameco . '</label>
                                        </td>
                                        <td>
                                            <input type="hidden" id="co_line" class="form-control border-primary" value="' . $row->co_line . '" readonly>
                                            <span class="form-control border-primary pseudoinput">' . $co_num_line . ' </span>
                                            <input type="hidden" name="co_line" class="form-control border-primary" value="' . $row->co_line . '">
                                            <input type="hidden" name="co_rel" class="form-control border-primary" value="' . $row->co_rel . '">
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><label>' . __('mobile.label.item_num') . '</label></td>
                                        <td colspan="4">';
                            foreach ($altBarCode as $barCode) {
                                $output .= '<span style="display: none"> ' . $barCode->alternate_barcode . ' </span>';
                            }
                            $output .= '
                                            <span class="form-control border-primary pseudoinput">' . $row->item_num . ' </span>
                                            <input readonly type="hidden" style="text-align:left;" name="item" class="form-control border-primary" value="' . base64_encode($row->item_num) . '">
                                        </td>
                                    </tr>
                                    <tr>
                                        <td width="70px"></td>
                                        <td colspan="4">
                                        <textarea readonly type="text" style="text-align:left;font-size:15px;" class="form-control border-primary">' . $row->item_desc . '</textarea>
                                            <input name="item_desc" readonly type="hidden" style="text-align:left;"  class="form-control border-primary" value="' . $row->item_desc . '">
                                        </td>
                                    </tr>




                                    <tr>
                                    <td><label>' . __('mobile.label.qty_required') . '</label></td>
                                    <td>
                                       <span class="form-control border-primary pseudoinput" style="text-align:right;" >' . numberFormatPrecision($row->qty_shortage, $unit_quantity_format, '.', '') . ' </span>
                                        <input readonly size="10" type="hidden" class="form-control border-primary" name="qty_req" style="text-align:right;" value="' . numberFormatPrecision($row->qty_shortage, $unit_quantity_format, '.', '') . '">
                                    </td>
                                    <td>
                                        <span class="form-control border-primary pseudoinput">' . $row->uom . ' </span>
                                        <input type="hidden" class="form-control border-primary" name="uom" value="' . base64_encode($row->uom) . '" readonly>
                                    </td>
                                </tr>


                              <tr>
                                        <td><label>' . __('mobile.label.qty_available') . '</label></td>
                                        <td>
                                           <span class="form-control border-primary pseudoinput" style="text-align:right;" >' . numberFormatPrecision(@$qty_avail, $unit_quantity_format, '.', '') . ' </span>

                                        </td>
                                        <td>
                                            <span class="form-control border-primary pseudoinput">  ' . @$qty_avail_uom . ' </span>

                                        </td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </form>
                ';
                        }
                    }
                }
            } else {
                $output = "
                    <tr>
                    <td align='center' colspan='6'>Record not found</td>
                    </tr>
                ";
            }


            $data = array(
                'table_data'  => $output,
                'total_data'  => $total_row
            );

            echo json_encode($data);
        }
    }

    public function pickCoValidation(Request $request)
    {
        $errors = [];

        if ($request['item_num'] && $request['item_num'] != "NON-INV") {
            $item_num = $request['item_num'];
            $result = ValidationController::checkItemNumValidation($item_num, $request['whse_num']);

            if ($result !== true) {
                $errors['item_num'] = $result;
            }
        }

        if ($request['from_loc']) {
            $result = ValidationController::checkTransitPickingLocValidtion($request, 'from_loc', true);
            $tparm = new TparmView;

            if ($result !== true) {
                $errors['from_loc'] = $result;
            }
        }

        if ($request['lot_num']) {
            $result = ValidationController::checkLotNumValidtion($request, false);
            $tparm = new TparmView;

            if ($result !== true) {
                $errors['lot_num'] = $result;
            }
        }

        $coitem = new CustomerOrderItem;
        $getCoItem = $coitem->where('co_num', $request->co_num)
            ->where('co_line', $request->co_line)
            ->where('co_rel', $request->co_rel)
            ->first();

        // Verifying COItem exist
        if (!$getCoItem) {
            $errors['co_num'] = __('error.mobile.notexist', ['resource' => '[' . $request->co_num . '-' . $request->co_line . '-' . $request->co_rel . ']']);
        }

        if ($getCoItem->rel_status != 'O') {
            $errors['co_num'] = __('error.mobile.status_is_completed', ['resource' => "CO-" . $request->co_num]);
        }

        $checkStageLoc = Loc::where('whse_num', $request->whse_num)->where('loc_num', $request->stage_num)->first();
        if ($checkStageLoc) {
            if ($checkStageLoc->loc_status == 0) {
                $errors['loc_num'] = 'Stage Location-' . $request->stage_num . ' cannot be proceed due to status is inactive';
            }

            if ($checkStageLoc->loc_type == 'T') {
                $errors['loc_num'] = 'Stage Location-' . $request->stage_num . ' cannot be proceed due to Location is a Transit Location';
            }
        } else {
            $errors['loc_num'] = 'Stage Location-' . $request->stage_num . ' not exist';
        }

        if ($request->lot_num != "") {
            $getCheckItemLocLot = LotLoc::where('loc_num', $request->loc_num)->where('lot_num', $request->lot_num)->where('whse_num', $request->whse_num)->where('item_num', $request->item_num)->exists();

            if ($getCheckItemLocLot == false) {
                // Lot Number does not exist.
                $errors['lot_num'] = 'Item Location Lot -' . $request->lot_num . ' not exist';
            }
        }

        if ($request->citem != "") {
            if ($getCoItem->item_num != $request->citem) {
                // Lot Number does not exist.
                $errors['item_num'] = __('error.mobile.not_match', ['resource' => __('mobile.label.item_num')]);
            }
        }

        if ($request['batch_id'] && checkBatchIdExists($request['batch_id'])) {
            throw ValidationException::withMessages([__('error.admin.batch_id_exists')]);
        }

        return $errors;
    }

    public function PickCo(Request $request)
    {
        //dd($request);
        // store loc_num to other variable
        // $loc_num_temp = $request['loc_num'];
        $temp_request = $request->all();

        // Store Session to do comparing with 3 minuts for same item , loc and lot
        //  $request->session()->put('pick_co', $request->all());
        // $request->session()->save();
        // Compare session
        // $session_data = $request->session()->get('pick_co');

        // dd($session_data);

        $validateErrors = self::pickCoValidation($request);

        $tparm = new TparmView;
        $sap_require_check_online = $tparm->getTparmValue('System', 'sap_check_online_connection');

        $tparm = new TparmView;
        $sap_trans_order_integration = $tparm->getTparmValue('System', 'sap_trans_order_integration');

        if ($sap_require_check_online == 1 && $sap_trans_order_integration == 1) {
            // Checking SAP Server
            $site_id = auth()->user()->site_id;
            $checkConnection = SapApiCallService::getSQPServerConnection($site_id, null, 'Job Receipt', 1);

            if ($checkConnection > 2) {
                Alert::error(__('error.mobile.sap_error'), __('error.mobile.sap_server_down'))->persistent('Dismiss');
                return redirect()->back();
            }
        }

        if (count($validateErrors) > 0) {
            return back()->withErrors($validateErrors)->withInput();
        }


        $coitem = new CustomerOrderItem;
        $getCoItem = $coitem->where('co_num', $request->co_num)
            ->where('co_line', $request->co_line)
            ->where('co_rel', $request->co_rel)
            ->first();

        // Verifying COItem exist
        // if (!$getCoItem) {
        //     throw ValidationException::withMessages([__('error.mobile.notexist', ['resource' => '[' . $request->co_num . '-' . $request->co_line . '-' . $request->co_rel . ']'])]);
        // }

        // if ($getCoItem->rel_status != 'O') {

        //     throw ValidationException::withMessages([__('error.mobile.status_is_completed', ['resource' => "CO-" . $request->co_num])]);
        // }

        // Verify stage loc
        // $checkStageLoc = Loc::where('whse_num', $request->whse_num)->where('loc_num', $request->stage_num)->first();
        // if ($checkStageLoc) {
        //     if ($checkStageLoc->loc_status == 0) {
        //         throw ValidationException::withMessages(['loc_num' => 'Stage Location-' . $request->stage_num . ' cannot be proceed due to status is inactive']);
        //     }

        //     if ($checkStageLoc->loc_type == 'T') {
        //         throw ValidationException::withMessages(['loc_num' => 'Stage Location-' . $request->stage_num . ' cannot be proceed due to Location is a Transit Location']);
        //     }
        // } else {
        //     throw ValidationException::withMessages(['loc_num' => 'Stage Location-' . $request->stage_num . ' not exist']);
        // }

        // Checking Item Loc Lot must exist
        // if ($request->lot_num != "") {
        //     $getCheckItemLocLot = LotLoc::where('loc_num', $request->loc_num)->where('lot_num', $request->lot_num)->where('whse_num', $request->whse_num)->where('item_num', $request->item_num)->exists();

        //     if ($getCheckItemLocLot == false) {
        //         // Lot Number does not exist.
        //         throw ValidationException::withMessages(['lot_num' => 'Item Location Lot -' . $request->lot_num . ' not exist']);
        //     }
        // }
        // dd($request,$getCheckItemLocLot);

        // Checking Item Num must exist
        // if ($request->citem != "") {
        //     if ($getCoItem->item_num != $request->citem) {
        //         // Lot Number does not exist.
        //         throw ValidationException::withMessages(['item_num' => __('error.mobile.not_match', ['resource' => __('mobile.label.item_num')])]);
        //     }
        // }



        // Rewrite the ReadOnly fields
        $request['co_num'] = $getCoItem->co_num;
        $request['co_line'] = $getCoItem->co_line;
        $request['co_rel'] = $getCoItem->co_rel;
        $request['cust_num'] = $getCoItem->cust_num;
        $request['whse_num'] = $getCoItem->whse_num;
        $request['item_num'] = $getCoItem->item_num;
        $request['qty_required'] = $getCoItem->qty_required;


        $request['co_uom'] = $getCoItem->uom;
        Session::forget('trans_num_store');
        // $uniqueidkey = base64_encode($request->co_num . $request->co_line . $request->co_rel . $request->cust_num.$request->whse_num .$request->stage_num.auth()->user()->site_id);
        //$lockKey = 'record_' . $uniqueidkey . '_lock';

        // Check if a lock exists
        // if (Cache::has($lockKey)) {

        //     throw ValidationException::withMessages(['This record is being edited by another user. Please try again later']);
        // }

        // Set a lock with a short expiration time
        // Cache::put($lockKey, true, now()->addSeconds(1));

        $record = array(
            'site_id' => auth()->user()->site_id,
            'cust_num' => $request->cust_num,
            'co_num' => $request->co_num,
            'co_line' => $request->co_line,
            'item_num' => $request->item_num,
            'qty_ship' => $request->qty,
            'whse_num' => $request->whse_num,
            'from' => $request->loc_num ?? NULL,
            'to' => $request->stage_num,
            'lot_num' => $request->lot_num ?? NULL,
            'shipment_id' => 0,
            'uom' => $request->uom,
            'qty_current' => $request->qty_available,
            //'pick_uniqekey' => base64_encode($request->whse_num.$request->stage_num.$request->co_num) ?? NULL,
            'pick_uniqekey' => base64_encode($request->whse_num . $request->stage_num . $request->co_num . $request->co_line) ?? NULL,
            'created_by' => auth()->user()->name,
            'modified_by' => auth()->user()->name
        );

        $lockKey = 'insert_lock_' . $record['pick_uniqekey'];
        $lock = Cache::lock($lockKey, 1); // Lock for 10 seconds

        $baseuom = $request->base_uom;
        $selectuom = $request->uom;
        $qty = $request->qty;
        $item_num = $request->item_num;
        $cust_num = $request->cust_num;
        $type_mode = $request->trans_type;


        //OCeanCash Customization 15
        $delivery_date = $request->delivery_date ?? "";
        $delivery_trip = $request->delivery_trip ?? "";

        // dd($delivery_date);
        $getQtyConv = UomConv::convertUOM($baseuom, $selectuom, $getCoItem->uom, $qty, $item_num, $cust_num, '', $type_mode);
        $qty_conv = $getQtyConv['conv_qty_to_line']['qty'];

        $tparmallowovership = new TparmView;
        $intAllowOverPick  = $tparmallowovership->getTparmValue('CustOrdShipping', 'allow_over_ship');
        $whse_num = $request->whse_num;
        $loc_num  = $request->loc_num;
        $lot_num  = $request->lot_num ?? NULL;

        $whse_num_encode = base64_encode($whse_num);
        $item_num_encode = base64_encode($item_num);
        $loc_num_encode = base64_encode($loc_num);
        $lot_num_encode = base64_encode($lot_num) ?? NULL;



        $getQtyAvailable = app('App\Http\Controllers\ApiController')->displayQtyItemAvailable($whse_num_encode, $item_num_encode, $loc_num_encode, $lot_num_encode);
        //dd($request,$getQtyAvailable['qty_available_conv']);
        if ($intAllowOverPick != 1) {
            if ($qty_conv > $getQtyAvailable['qty_available_conv']) {
                // throw ValidationException::withMessages(['Qty must be less than or equal to Qty Available [' . $getQtyAvailable['qty_available_conv'].']']);


            if($getQtyAvailable['qty_available_conv'] > 1)
                    {
                       $qyt_show = $getQtyAvailable['qty_available_conv'];
                    }
                    else{

                         $qyt_show = "0.00000000";
                    }
                    //throw ValidationException::withMessages([__('error.mobile.qty_move_more_qty_available') ]);
                     //throw ValidationException::withMessages([__('error.mobile.qty_move_more_qty_available')."[ ".$qyt_show." ]"]);
                     $errors = __('error.mobile.co_pick_qty_qty_available')." [ ".$qyt_show." ].";





            }
            if ($qty_conv > $getCoItem->qty_shortage) {
                throw ValidationException::withMessages(['Qty must be less than or equal to Qty Required [' . $getCoItem->qty_shortage.'].']);
            }
        }

        $request = validateSansentiveValue($request);
        DB::beginTransaction();
        try {

            // Skip checking for NON-INV n Child
            if ($request->getCheckNONINV == 0) {
                $request->validate([
                    'loc_num' => 'required|exists:locs,loc_num,whse_num,' . $request->whse_num . ',loc_status,1,site_id,' . auth()->user()->site_id,
                ], [
                    'loc_num.exists' =>  __('error.mobile.processinactive', ['resource' => __('mobile.list.locations')]),
                ]);
            }


            $request['original_loc_num'] =  $request->loc_num ?? NULL;

            $pickuniqkey = base64_encode($request->whse_num . $request->stage_num . $request->co_num . $request->co_line);
            // $checkStagingLine = StagingLine::where('pick_uniqekey',$pickuniqkey)->get();

            $sessionkeytrans_num =  Session::get('trans_num_store');
            $checkMatl  = MatlTrans::where('trans_num', $sessionkeytrans_num)->where('site_id', auth()->user()->site_id)->exists();
            // dd($sessionkeytrans_num);

            if ($checkMatl) {
                //Alert::error('Validation Error', __('error.mobile.duplicate_trans'))->persistent('Close');

                throw ValidationException::withMessages([__('error.mobile.duplicate_trans')]);
            } else {
                if ($lock->get()) {

                    DB::table('staging_lines')->lockForUpdate();
                    DB::table('malt_trans')->lockForUpdate();
                    DB::table('item_locs')->lockForUpdate();
                    DB::table('stage_locs')->lockForUpdate();
                    DB::table('lot_locs')->lockForUpdate();


                    $exists = StagingLine::where('pick_uniqekey', $pickuniqkey)->where('whse_num', $request->whse_num)->where('from', $request->loc_num)->where('to', $request->stage_num)->where('site_id', auth()->user()->site_id)->exists();

                    if ($exists) {
                        $created_date = StagingLine::where('pick_uniqekey', $pickuniqkey)->where('whse_num', $request->whse_num)->where('from', $request->loc_num)->where('to', $request->stage_num)->where('site_id', auth()->user()->site_id)->orderby('id', 'DESC')->first();
                        $datetimeWithAddedSeconds = $created_date->created_date->addSeconds(10);
                        $currentDatetime = Carbon::now();

                        if ($datetimeWithAddedSeconds->gt($currentDatetime)) {
                            //if ($currentDatetime->gt($datetimeWithAddedSeconds)) {
                            throw ValidationException::withMessages(['This record concurrent posting. Please try again later']);
                        }

                        //dd($currentDatetime, $datetimeWithAddedSeconds);
                    }
                    $staginglinesid = StagingLine::create($record)->id;

                    // dd($staginglinesid,$exists,$pickuniqkey,$request->whse_num,$request->loc_num,$request->stage_num);
                    $request['staginglines_id'] = $staginglinesid;
                    //dd('jdjdhd', date('H:i:s'),$lock,$exists);
                    $this->pickShipService->executeCoPick($request);
                    Session::forget('trans_num_store');
                } else {
                    throw ValidationException::withMessages(['Another user is currently editing this record. Please try again later']);
                }
            }

            // Check the co_num
            // if($request->getCheckNONINV==1){
            //     $getCheckSAPNONINV =  DB::table('coitems_sap_exts')->select('id')->where(
            //         [
            //             ['co_num', '=', $request->co_num],
            //             ['co_line', '=', $request->co_line],
            //             ['site_id', '=', auth()->user()->site_id]
            //         ]
            // )->whereNotNull('family_line')->exists();
            //     }


            DB::commit();


        } catch (Exception $e) {
            DB::rollback();

            if (isset($temp_request)) {
                // Revert back loc_num changes in request
                // $request['loc_num'] = $loc_num_temp;
                $request->merge($temp_request);
            }

            throw $e;
        } finally {
            $lock->release();
        }

        Alert::success('Success', __('success.processed', ['process' => __('Customer Order Picking')]))->persistent('Close');
        $tparm = new TparmView;
        $sap_trans_order_integration = $tparm->getTparmValue('System', 'sap_trans_order_integration');
        $sap_single_bin = $tparm->getTparmValue('System', 'sap_single_bin');
        if ($sap_trans_order_integration == 1 && $request->item_num != "NON-INV" && $request->getCheckNONINV == 0) {
            //$result = SapCallService::postCOPicking($request);
            // if (config('icapt.enable_sap_ap_readfrom_maltrans')) {
            if ($sap_single_bin == 1) {
                $result = 200;
            } else {

                $result = SapCallService::postStockTransferFromMaltrans('CO Pick', $request);
                //$result_notice = SapCallService::postPickUnpickNotification($request,'COPick');


                /*} else {
                    $result = SapCallService::postCOPicking($request);
                }*/
                if ($result != 200) {
                    Alert::error(__('error.mobile.sap_error'), __('error.mobile.sap_error_contact') . $result)->persistent('Dismiss');
                }
            }

            // if ($result_notice != 200) {
            //     Alert::error(__('error.mobile.sap_error'), __('error.mobile.sap_error_contact') . $result_notice)->persistent('Dismiss');
            // }
        }


        // Send error if lot_num is expired and allow_expired_item = 0
        LotService::checkExpiryDate('allow_expired_item', $request);
        $transType = config('constants.modules.CustOrdShipping');


        Session::put('modulename', 'COPick');
        Session::put('co_num', $request->co_num);
        Session::put('whse_num', $request->whse_num);
        Session::put('stage_num', $request->stage_num);
        Session::put('pick_by', $request->pick_by);




        if ($request->lot_num != null) {
            $check_expiry_date = LotService::getExpiryDate($request);

            // Generate barcode
            $input = BarcodeController::GetCustOrdLabelData($request->co_num, $request->co_line, $request->co_rel, $request->qty, $request->uom, $request->whse_num, $request->stage_num, $request->lot_num, $check_expiry_date, null, 'CustOrdPicking');
        } else {
            // Generate barcode
            $input = BarcodeController::GetCustOrdLabelData($request->co_num, $request->co_line, $request->co_rel, $request->qty, $request->uom, $request->whse_num, $request->stage_num, $request->lot_num, null, null, 'CustOrdPicking');
        }

        $tparm = new TparmView;
        $print_label = $tparm->getTparmValue('CustOrdPicking', 'print_label');

        $strQTPicked = $request->qty;
        $strQtyRequired = $request->qty_required_conv;
        $finalDeduct = ($strQtyRequired - $strQTPicked);
        Session::put('qty_required', $finalDeduct);

        if ($print_label == 1) {

            $modulename_action =  session()->put('modulename_action', 'COPick');
            return BarcodeController::showLabelDefinition($input);
        } else {

            $pick_by = session()->get('pick_by_as');
            $select_pick_by = session()->get('select_pick_by');

            $shipping_zone_code =  session()->get('shipping_zone_code');
            $strsales_person = session()->get('strsales_person');
            $item_num =  session()->get('item_num');
            $cust_num =  session()->get('cust_num');


            //dd($finalDeduct,$strQtyRequired,$strQTPicked,$request);
            if ($finalDeduct > 0) {


                $input['all'] = $request->all();
                $input['count_list'] = 1;
                // dd($request,$input);
                $co_list = CoPickView::where('co_num', $request->co_num)
                    ->where('whse_num', $request->whse_num)
                    ->select('co_num', 'co_line', 'co_rel', 'uom', 'item_num', 'item_desc', 'qty_released', 'qty_shipped', 'qty_shortage', 'qty_returned', DB::raw("IFNULL(SUM(qty_staged),0) as qty_staged"))
                    ->orderBy('co_num')
                    ->groupBy('co_line')
                    ->groupBy('co_rel');


                if ($item_num && $item_num != "NON-INV") {
                    $co_list = $co_list->where('item_num', $item_num);
                }
                if ($input['all']['co_line']) {
                    //  $co_list = $co_list->where('co_line', 'like', '%' . $input['all']['co_line'] . '%');
                }


                $co_list = $co_list->get();
                foreach ($co_list as $co) {
                    $qty_shortage = $co->qty_shortage;
                }

                return  redirect()->route('CoPickingProcess', [
                    'totalrecord' => $input['count_list'],
                    'stage_num' => base64_encode($request->stage_num),
                    'co_num' =>  base64_encode($request->co_num),
                    'delivery_date' => @$input['all']['delivery_date'],
                    'delivery_trip' => @$input['all']['delivery_trip'],
                    'co_line' => $input['all']['co_line'],
                    'co_rel' => @$input['all']['co_rel'] ?? 0,
                    'item' =>  $input['all']['item']  ?? $input['all']['item_num'],
                    'uom' => base64_encode($request->uom),
                    'item_desc' => @$input['all']['item_desc'] ?? null,
                    'whse_num' => base64_encode($request->whse_num),
                    'qty_req' => $qty_shortage
                ]);




                //return redirect()->route('CoPickingProcess', $requestC, 303);
            }





            if ($select_pick_by == "Customer") {
                return redirect()->route('CoPickingDetails', ['cust_num' => $cust_num, 'pick_by' => $pick_by, 'select_pick_by' => $select_pick_by, 'shipping_zone_code' => $shipping_zone_code, 'strsales_person' => $strsales_person,  'item_num' => $item_num, 'whse_num' => $request->whse_num, 'co_num' => base64_encode($request->co_num), 'co_line' => "", 'stage_num' => $request->stage_num, 'delivery_date' => $delivery_date, 'delivery_trip' => $delivery_trip]);
            } else {

                return redirect()->route('CoPickingDetails', ['whse_num' => $request->whse_num, 'co_num' => $request->co_num, 'stage_num' => $request->stage_num, 'delivery_date' => $delivery_date, 'delivery_trip' => $delivery_trip]);
            }
            // return app('App\Http\Controllers\RouteController')->BackButton();
        }















    }

    public function runCoPickCWProcess(Request $request)
    {
        $batch_id = $request->batch_id;

        if ($batch_id && checkBatchIdExists($batch_id)) {
            throw ValidationException::withMessages([__('error.admin.batch_id_exists')]);
        }

        // Validate CO
        $co_num = $request->ref_num;
        $co_line = $request->ref_line;
        if (empty($co_num) || empty($co_line))
        {
            throw ValidationException::withMessages([ __('error.mobile.does_not_exists', ['resource' => __('Customer Order') ]) ]);
        }

        $co_item = CustomerOrderItem::where('co_num', $co_num)->where('co_line', $co_line)->first();
        if (!$co_item)
        {
            throw ValidationException::withMessages([ __('error.mobile.does_not_exists', ['resource' => __('Customer Order') ]) ]);
        }

        // Get Tolerance and UOM - for CO picking, tolerance is the qty shortage
        $tolerance = $co_item->qty_released - $co_item->qty_shipped + $co_item->qty_returned;
        $tolerance_uom = $co_item->uom;

        // Validate Catch Weight Data
        CatchWeightService::validateCatchWeightData($request, $tolerance, $tolerance_uom);

        $siteSettings = new SiteSetting();
        $this->timezone = $siteSettings->getTimezone();
        $now =  Timezone::convertFromUTC(now(), $this->timezone, SiteSetting::getOutputDateFormat() .' H:i:s');

        $tparm = new TparmView;
        $sap_require_check_online = $tparm->getTparmValue('System', 'sap_check_online_connection');
        $tparm = new TparmView;
        $sap_trans_order_integration = $tparm->getTparmValue('System', 'sap_trans_order_integration');

        DB::beginTransaction();

        try {
            // Get record data for processing
            $getRecordData = $request->all();
            $pass_uom = $tolerance_uom;

            // Get base UOM for the item
            $baseuom = Item::where('item_num', $request->item_num)->value('uom');

            $request->merge([
                'original_uom' => $tolerance_uom,
                'base_uom' => $baseuom,
                'trans_type' => config('constants.modules.CustOrdPicking'),
                'co_num' => $co_num,
                'co_line' => $co_line,
                'co_rel' => $request->co_rel ?? 0,
            ]);

            // Convert quantities for CO processing
            $cust_num = $request->cust_num;
            $type_mode = __('mobile.nav.co_picking');
            $lineuom = $co_item->uom;

            // Calculate total quantity from catch weight lots
            $total_catch_weight_qty = array_sum($request->arr_qty ?? []);

            $getQtyConv = UomConv::convertUOM($baseuom, $pass_uom, $lineuom, $total_catch_weight_qty, $request->item_num, $cust_num, '', $type_mode);
            $co_pick_qty = $getQtyConv['qty'];

            $transType = config('constants.modules.CustOrdPicking');

            if ($sap_trans_order_integration != 1) {
                // Update inventory and create material transactions for catch weight
                $updateItemLocation = CatchWeightService::updateItemLocLotNMatlTrans($request, $tolerance_uom, $transType, "CO Pick");

                // Update CO item quantities - use converted quantity
                $updateCoItem = CoPickService::updateCoLineRel($request->co_num, $request->co_line, $request->co_rel, $co_pick_qty, 'COPick');

                if (!$updateCoItem) {
                    Alert::error('Error', 'Qty to pick is more than Qty required');
                    return redirect()->back();
                }
            }

            DB::commit();

            Alert::success('Success', __('success.processed', ['process' => __('Customer Order Picking - Catch Weight')]));

            // Redirect back to CO picking list
            return redirect()->route('CoPickingDetails', [
                'whse_num' => $request->whse_num,
                'co_num' => $request->co_num,
                'stage_num' => $request->stage_num
            ]);

        } catch (\Exception $e) {
            DB::rollback();
            throw $e;
        }
    }

    public function validation(Request $request)
    {

        $vinput = $request->all();

        foreach ($vinput as $name => $value) {
            if ($name == "co_num") {
                $model = new CustomerOrderItem();
                if ($request->whse_num != "") {
                    $result = $model->where('whse_num', $request->whse_num)->where('co_num', $value)->first();

                    // $result = $model->where('whse_num', $request->whse_num)->exists($value);
                } else {
                    $result = $model->exists($value);
                }
            }
            if ($name == "co_line") {
                $model = new CustomerOrderItem();
                $result = $model->checkLineExists($request->whse_num, $request->co_num, $request->co_line);
            }
            if ($name == "stage_num" || $name == "loc_num") {
                $model = new Loc();
                $result = $model->exists($value);
            }
            if ($name == "item_num") {
                $model = new CustomerOrderItem();
                $result = $model->itemExists($value);
            }

            if ($result == true) {
                return "true";
            } else {
                return "false";
            }
        }
    }

    public function COOpenvalidation(Request $request)
    {

        $co_num = $request->co_num;

        // $model = new CustomerOrderItem();
        $model = new CustomerOrder();
        $result = $model->where('co_num', '=', '' . $co_num . '');

        if ($result->exists()) {
            // if ($result->first()->rel_status != "O") {
            //     return 'inactive';
            // }
            if ($result->first()->co_status != "O") {
                return 'inactive';
            }

            return "true";
        } else {
            return "false";
        }
    }

    public function checkExpiryDateCOPicking(Request $request)
    {
        // Send error if lot_num is expired and allow_expired_item = 0
        return LotService::checkExpiryDate('allow_expired_item', $request);
    }

    //Pick Co by pallet process
    public function PickCobyPalletProcess(Request $request)
    {
        $select_pick_by = session()->get('select_pick_by') ?? null;

        // dd($request);

        // Send error if lpn_num's is not exist
        $checkLpn = Container::where('lpn_num', $request->lpn_num_field)->exists();
        if (!$checkLpn) {
            throw ValidationException::withMessages(['lpn_num' => 'LPN-' . $request->lpn_num . ' is not exist.']);
        }

        // Check Validation for the stage_loc
        $strCoNum =  $request->co_num;
        //$checkCoDetails = StageLoc::where('co_num',$strCoNum)->where('lpn_num',)->count();


        $checkCoDetails =    DB::table('stage_locs')
            ->whereNull('lpn_num')
            ->where('co_num', $strCoNum)
            ->count();
        // dd($checkCoDetails);
        if ($checkCoDetails > 0) {
            // Incompatible picking method. Order has unit-picked lines. Choose 'Pick By Unit' for the entire order.

            // Alert::error('Error', __('error.admin.copickclose'));


            Alert::error('Error', __('error.mobile.pick_unit_error'));
            return back();
        }


        // Get Pallet Loc
        DB::beginTransaction();
        try {
            $stageLoc = $request->stage_loc;
            $lpnNum = $request->lpn_num_field;
            $getPalletLoc = Container::where('lpn_num', $request->lpn_num_field)->first();

            // $getPalletLiners = ContainerItem::where('lpn_num', $request->lpn_num_field)->get();
            // $arrStirePalletDetails = array();
            // foreach($getPalletLiners as $keyPallet)
            // {
            // $arrStirePalletDetails[$keyPallet->lpn_line] = $keyPallet;

            // }
            // dd($request);
            for ($i = 1; $i <= $request->count; $i++) {

                $co_num = 'ref_num_' . $i;
                $co_line = 'ref_line_' . $i;
                $co_rel = 'ref_rel_' . $i;
                $lpn_line = 'lpn_line_' . $i;
                $item = 'item_' . $i;

                $item = utf8_encode(base64_decode($request->$item));
                $item = htmlspecialchars_decode($item);

                $item_desc = 'item_desc_' . $i;
                $qty_req = 'qty_transact_' . $i;
                $uom = 'qty_transact_uom_' . $i;
                $qty_to_pick = 'qty_input_' . $i;
                $uom_to_pick = 'qty_input_uom_' . $i;

                $lot_num = 'lot_num_' . $i;

                if ($select_pick_by == "Customer") {
                    $datasparate[$i] = explode("-",  $request->$co_line);

                    // dd($datasparate[$i]);
                    $request->merge([
                        'co_num' => $datasparate[$i][0],
                        $co_line => $datasparate[$i][1],
                    ]);
                }


                $palletLoc = $getPalletLoc->loc_num;
                $each_lpn_line = explode(",", $request->$lpn_line);
                // dd(count($a));
                // check stage loc freeze
                $check_stage_loc = ItemLoc::where('whse_num', $request->whse_num)->where('loc_num', $request->stage_loc)->where('item_num', $item)->value('freeze');
                if ($check_stage_loc == 'Y') {
                    throw ValidationException::withMessages([__('error.mobile.loc_freeze', ['resource' => $item, 'resource2' => $request->stage_loc])]);
                }

                // check lpn loc freeze
                $check_lpn_loc = ItemLoc::where('whse_num', $request->whse_num)->where('loc_num', $palletLoc)->where('item_num', $item)->value('freeze');
                if ($check_lpn_loc == 'Y') {
                    throw ValidationException::withMessages([__('error.mobile.loc_freeze', ['resource' => $item, 'resource2' => $palletLoc])]);
                }

                //Get Item qty bal
                $itemQty = ItemWarehouse::select('qty_on_hand')->where('whse_num', $request->whse_num)->where('item_num', $item)->first();
                // dd($itemQty,$request);
                // if(isset($arrStirePalletDetails[$i]))
                // {

                if ($request->$lpn_line != null) {
                    if (count($each_lpn_line) > 1) {
                        foreach ($each_lpn_line as $each_line) {
                            $lpn_each_qty = 'lpn_each_qty' . $each_line;
                            $record[$i][$each_line] =
                                [
                                    'whse_num' => $request->whse_num,
                                    'stage_loc' => $stageLoc,
                                    'cust_num' => $request->cust_num,
                                    'co_num' => $request->co_num,
                                    'co_line' => $request->$co_line,
                                    'co_rel' => $request->$co_rel,
                                    'lpn_num' => $lpnNum,
                                    'lpn_line' => $request->$lpn_line,
                                    'loc_num' => $getPalletLoc->loc_num,
                                    'lot_num' => $request->$lot_num,
                                    'item_num' => $item,
                                    'item_desc' => $request->$item_desc,
                                    'qty_req' => $request->$qty_req,
                                    'base_uom' => $request->$uom,
                                    'qty_input' => $request->$lpn_each_qty,
                                    'uom' => $request->$uom_to_pick,
                                    'transtype' => __('mobile.nav.co_picking'),
                                    'each_lpn_line' => $each_line,
                                ];


                            $coPick[$i][$each_line] =
                                [
                                    'site_id' => auth()->user()->site_id,
                                    'cust_num' => $request->cust_num,
                                    'co_num' => $request->co_num,
                                    'co_line' => $request->$co_line,
                                    'co_rel' => $request->$co_rel,
                                    'item_num' => $item,
                                    'line_uom' => $request->$uom,
                                    'qty_ship_co' => $request->$qty_req,
                                    'qty_ship_lpn' => $request->$lpn_each_qty,
                                    'whse_num' => $request->whse_num,
                                    'from' => $getPalletLoc->loc_num,
                                    'to' => $stageLoc,
                                    'lot_num' => $request->$lot_num  ?? NULL,
                                    'shipment_id' => 0,
                                    'uom' => $request->$uom_to_pick,
                                    'qty_current' => $itemQty->qty_on_hand,
                                    'pick_uniqekey' => base64_encode($request->whse_num . $stageLoc . $request->co_num . $request->$co_line) ?? NULL,
                                    'created_by' => auth()->user()->name,
                                    'modified_by' => auth()->user()->name,
                                    'loc_num' => $getPalletLoc->loc_num,
                                    'lpn_num' => $lpnNum,
                                    'lpn_line' => $request->$lpn_line,
                                    'stage_num' => $stageLoc,
                                    'each_lpn_line' => $each_line,
                                ];
                        }
                    } else {
                        $record[$i] =
                            [
                                'whse_num' => $request->whse_num,
                                'stage_loc' => $stageLoc,
                                'cust_num' => $request->cust_num,
                                'co_num' => $request->co_num,
                                'co_line' => $request->$co_line,
                                'co_rel' => $request->$co_rel,
                                'lpn_num' => $lpnNum,
                                'lpn_line' => $request->$lpn_line,
                                'loc_num' => $getPalletLoc->loc_num,
                                'lot_num' => $request->$lot_num,
                                'item_num' => $item,
                                'item_desc' => $request->$item_desc,
                                'qty_req' => $request->$qty_req,
                                'base_uom' => $request->$uom,
                                'qty_input' => $request->$qty_to_pick,
                                'uom' => $request->$uom_to_pick,
                                'transtype' => __('mobile.nav.co_picking'),
                            ];


                        $coPick[$i] =
                            [
                                'site_id' => auth()->user()->site_id,
                                'cust_num' => $request->cust_num,
                                'co_num' => $request->co_num,
                                'co_line' => $request->$co_line,
                                'co_rel' => $request->$co_rel,
                                'item_num' => $item,
                                'line_uom' => $request->$uom,
                                'qty_ship_co' => $request->$qty_req,
                                'qty_ship_lpn' => $request->$qty_to_pick,
                                'whse_num' => $request->whse_num,
                                'from' => $getPalletLoc->loc_num,
                                'to' => $stageLoc,
                                'lot_num' => $request->$lot_num  ?? NULL,
                                'shipment_id' => 0,
                                'uom' => $request->$uom_to_pick,
                                'qty_current' => $itemQty->qty_on_hand,
                                'pick_uniqekey' => base64_encode($request->whse_num . $stageLoc . $request->co_num . $request->$co_line) ?? NULL,
                                'created_by' => auth()->user()->name,
                                'modified_by' => auth()->user()->name,
                                'loc_num' => $getPalletLoc->loc_num,
                                'lpn_num' => $lpnNum,
                                'lpn_line' => $request->$lpn_line,
                                'stage_num' => $stageLoc,
                            ];
                    }
                    //dd($request, $record, $coPick);
                }

                // }
            }

            // CO : 1,2,3  Pallet : 1,2
            // Co 3
            // dd($record,$coPick);
            if (!empty($record)) {
                $sendResponseFrom = CoPickController::processPalletCoTrans(config('icapt.transtype.co_pick_from'), $record);
                $sendResponseTo = CoPickController::processPalletCoTrans(config('icapt.transtype.co_pick_to'), $record);
                $updatePalletLoc = PalletService::updatePalletLoc($lpnNum, $stageLoc, '', 'CO Pick');
                $updateCo = CoPickController::palletCoProcess($coPick);

                Alert::success('Success', __('success.processed', ['process' => __('Customer Order Picking')]))->persistent('Close');
                Session::put('modulename', 'COPick');
                Session::put('co_num', $request->co_num);
                Session::put('whse_num', $request->whse_num);
                Session::put('stage_num', $stageLoc);
                Session::put('pick_by', "pallet");

                DB::commit();

                // SAP Intergration
                $tparm = new TparmView;
                $sap_trans_order_integration = $tparm->getTparmValue('System', 'sap_trans_order_integration');

                if ($sap_trans_order_integration == 1 && ($getPalletLoc->loc_num != $stageLoc)) {
                    $lpnnum = $request->lpn_num_field;
                    // dd("sxsx",$request);

                    $result = SapCallService::postPalletLPN($lpnnum, 'CO Pick');

                    //if (config('icapt.enable_sap_ap_readfrom_maltrans')) {
                    if ($result != 200) {
                        Alert::error(__('error.mobile.sap_error'), __('error.mobile.sap_error_contact') . $result)->persistent('Dismiss');
                    }
                    //}
                }

                if ($request->lot_num != null) {
                    // Generate barcode
                    $input = BarcodeController::GetCustOrdLabelData($request->co_num, $request->$co_line, $request->$co_rel, $request->$qty_to_pick, $request->$uom, $request->whse_num, $stageLoc, $request->lot_num, null, null, 'CustOrdPicking');
                } else {
                    // Generate barcode
                    $input = BarcodeController::GetCustOrdLabelData($request->co_num, $request->$co_line, $request->$co_rel, $request->$qty_to_pick, $request->$uom, $request->whse_num, $stageLoc, $request->lot_num, null, null, 'CustOrdPicking');
                }

                $tparm = new TparmView;
                $print_label = $tparm->getTparmValue('CustOrdPicking', 'print_label');

                if ($print_label == 1) {
                    return BarcodeController::showLabelDefinition($input);
                } else {
                    $getTotalLineCo = CustomerOrderItem::where('co_num', $request->co_num)->where('qty_shortage', '>', 0)->count();
                    if (count($record) < $getTotalLineCo) {
                        return redirect()->route('CoPickingDetails', ['whse_num' => $request->whse_num, 'co_num' => $request->co_num, 'stage_num' => $stageLoc]);
                    } else {
                        return redirect()->route('CoPick');
                    }
                }
            } else {
                throw ValidationException::withMessages(["Selected Pallet is empty. Please select other Pallet"]);
            }
        } catch (Exception $e) {
            DB::rollback();
            throw $e;
        }
    }

    public function processPalletCoTrans($transtype, $record)
    {
        $recod_key = array_keys($record);
        //$getLpnDetailsQTY = ContainerItem::where('lpn_num', $record[$recod_key[0]]['lpn_num'])->orderBy('lpn_line', 'ASC')->get();


        // Check LPN Line
        // $getLPNCount = explode(",",$record[$recod_key[0]]['lpn_line']);
        // $intIndexcOUNT =1;
        // if(count($getLPNCount ?? []) > 1)
        // {
        //     foreach($getLpnDetailsQTY as $key)
        //     {

        //         $record[$intIndexcOUNT]['whse_num'] = $record[$intIndexcOUNT]['whse_num'];
        //         $record[$intIndexcOUNT]['co_num'] = $record[$intIndexcOUNT]['co_num'];
        //         $record[$intIndexcOUNT]['co_line'] = $record[$intIndexcOUNT]['co_line'];
        //         $record[$intIndexcOUNT]['lpn_num'] = $record[$intIndexcOUNT]['lpn_num'];
        //         $record[$intIndexcOUNT]['lpn_line'] = $key->lpn_line;
        //         $record[$intIndexcOUNT]['from_loc'] = $record[$intIndexcOUNT]['loc_num'];
        //         $record[$intIndexcOUNT]['stage_loc'] = $record[$intIndexcOUNT]['stage_loc'];
        //         $record[$intIndexcOUNT]['lot_num'] = $key->lot_num;
        //         $record[$intIndexcOUNT]['item_num'] = $record[$intIndexcOUNT]['item_num'];

        //         $record[$intIndexcOUNT]['item_desc'] = $record[$intIndexcOUNT]['item_desc'];
        //         $record[$intIndexcOUNT]['qty_req'] = $record[$intIndexcOUNT]['qty_req'];
        //         $record[$intIndexcOUNT]['base_uom'] = $record[$intIndexcOUNT]['base_uom'];
        //         $record[$intIndexcOUNT]['qty_input'] =$key->qty_contained;
        //         $record[$intIndexcOUNT]['uom'] = $record[$intIndexcOUNT]['uom'];




        //     }

        // }

        // dd($getLpnDetailsQTY, $record);
        // "lpn_num" => "FA-170004"
        // "lpn_line" => "1"
        // "lot_num" => "1"
        // "item_num" => "FA-170004"
        // "qty_contained" => "100.00000"
        // "qty_allocated" => "0.00"
        // "uom" => "UOM260223"
        // "whse_num" => "MAINWH 26/02/2023"
        // "stage_loc" => "Location 01"
        // "cust_num" => "Customer 260223"
        // "co_num" => "FA-170004"
        // "co_line" => "1"
        // "co_rel" => "0"
        // "lpn_num" => "FA-170004"
        // "lpn_line" => "1"
        // "loc_num" => "Loc 26022304"
        // "lot_num" => "1"
        // "item_num" => "FA-170004"
        // "item_desc" => "FA-170004"
        // "qty_req" => "200.00"
        // "base_uom" => "UOM260223"
        // "qty_input" => "200.00"
        // "uom" => "UOM260223"
        // "transtype" => "CO Picking"
        $arrStoreData = array();
        $intIndex = 1;
        $intLpnIndex = 1;
        foreach ($record as $dataDatails) {

            //check if contains more than 1 lpn line
            $isKeyNumeric = is_numeric(implode("", array_keys($dataDatails))); // check if array keys are numeric by combining all keys (because keys not start from 0)
            if (count($dataDatails) > 1 && $isKeyNumeric) {

                foreach ($dataDatails as $key => $lpnDet) {
                    $arrStoreData[$intLpnIndex]['whse_num'] = $lpnDet['whse_num'];
                    $arrStoreData[$intLpnIndex]['co_num'] = $lpnDet['co_num'];
                    $arrStoreData[$intLpnIndex]['co_line'] = $lpnDet['co_line'];
                    $arrStoreData[$intLpnIndex]['lpn_num'] = $lpnDet['lpn_num'];
                    $arrStoreData[$intLpnIndex]['lpn_line'] = $lpnDet['each_lpn_line'];
                    $arrStoreData[$intLpnIndex]['from_loc'] = $lpnDet['loc_num'];
                    $arrStoreData[$intLpnIndex]['stage_loc'] = $lpnDet['stage_loc'];
                    $arrStoreData[$intLpnIndex]['lot_num'] = ContainerItem::where('lpn_num', $lpnDet['lpn_num'])->where('lpn_line', $lpnDet['each_lpn_line'])->value('lot_num') ?? '';
                    $arrStoreData[$intLpnIndex]['item_num'] = $lpnDet['item_num'];

                    $arrStoreData[$intLpnIndex]['item_desc'] = $lpnDet['item_desc'];
                    $arrStoreData[$intLpnIndex]['qty_req'] = $lpnDet['qty_req'];
                    $arrStoreData[$intLpnIndex]['base_uom'] = $lpnDet['base_uom'];
                    $arrStoreData[$intLpnIndex]['qty_input'] = $lpnDet['qty_input'];
                    $arrStoreData[$intLpnIndex]['uom'] = $lpnDet['uom'];
                    $intLpnIndex++;
                }
            } else {
                $getLPNCount = explode(",", $dataDatails['lpn_line']);
                $getLpnDetailsQTY = ContainerItem::where('lpn_num', $dataDatails['lpn_num'])->whereIn('lpn_line', $getLPNCount)->orderBy('lpn_line', 'ASC')->get();

                foreach ($getLpnDetailsQTY as $getLpnDetail) {
                    $arrStoreData[$intLpnIndex]['whse_num'] = $record[$intIndex]['whse_num'];
                    $arrStoreData[$intLpnIndex]['co_num'] = $record[$intIndex]['co_num'];
                    $arrStoreData[$intLpnIndex]['co_line'] = $record[$intIndex]['co_line'];
                    $arrStoreData[$intLpnIndex]['lpn_num'] = $getLpnDetail->lpn_num;
                    $arrStoreData[$intLpnIndex]['lpn_line'] = $getLpnDetail->lpn_line;
                    $arrStoreData[$intLpnIndex]['from_loc'] = $record[$intIndex]['loc_num'];
                    $arrStoreData[$intLpnIndex]['stage_loc'] = $record[$intIndex]['stage_loc'];
                    $arrStoreData[$intLpnIndex]['lot_num'] = $getLpnDetail->lot_num;
                    $arrStoreData[$intLpnIndex]['item_num'] = $record[$intIndex]['item_num'];

                    $arrStoreData[$intLpnIndex]['item_desc'] = $record[$intIndex]['item_desc'];
                    $arrStoreData[$intLpnIndex]['qty_req'] = $record[$intIndex]['qty_req'];
                    $arrStoreData[$intLpnIndex]['base_uom'] = $record[$intIndex]['base_uom'];
                    $arrStoreData[$intLpnIndex]['qty_input'] = $record[$intIndex]['qty_input'];
                    $arrStoreData[$intLpnIndex]['uom'] = $getLpnDetail->uom;

                    $intLpnIndex++;
                }
            }
            // Check LPN Line
            // $getLPNCount = explode(",",$dataDatails['lpn_line']);
            // $getLpnDetailsQTY = ContainerItem::where('lpn_num', $dataDatails['lpn_num'])->whereIn('lpn_line', $getLPNCount)->orderBy('lpn_line', 'ASC')->get();
            // foreach($getLpnDetailsQTY as $getLpnDetail){

            // }


            $intIndex++;
        }

        //    dd($arrStoreData);
        foreach ($arrStoreData as $datas) {
            $transData = new Request($datas);
            $sendResponse = PalletService::palletMatlTrans($transtype, $transData);
        }
        return 'true';
    }

    public function palletCoProcess($record)
    {


        $recod_key = array_keys($record);



        // dd($record, $getLpnDetailsQTY);
        // $index = 0;
        // foreach($record as $dataline){
        //     // $getLpnDetailsQTY = ContainerItem::where('lpn_num', $dataline['lpn_num'])->where('item_num', $dataline['item_num'])->orderBy('lpn_line', 'ASC')->get();
        //     // foreach($getLpnDetailsQTY as $getLpnDetails){
        //     //     $recordMashup[$index]['site_id'] = $dataline['site_id'];
        //     //     $recordMashup[$index]['cust_num'] =  $dataline['cust_num'];
        //     //     $recordMashup[$index]['co_num'] = $dataline['co_num'];
        //     //     $recordMashup[$index]['co_line'] = $dataline['co_line'];
        //     //     $recordMashup[$index]['co_rel'] = $dataline['co_rel'];
        //     //     $recordMashup[$index]['item_num'] = $dataline['item_num'];
        //     //     $recordMashup[$index]['qty_ship'] = $getLpnDetails->qty_contained;
        //     //     $recordMashup[$index]['whse_num'] = $dataline['whse_num'];
        //     //     $recordMashup[$index]['lot_num'] = $getLpnDetails->lot_num;
        //     //     $recordMashup[$index]['shipment_id'] = 0;
        //     //     $recordMashup[$index]['base_uom'] = $dataline['uom'];
        //     //     $recordMashup[$index]['uom'] = $dataline['line_uom'];
        //     //     $recordMashup[$index]['qty_current'] = $getLpnDetails->qty_contained;
        //     //     $recordMashup[$index]['pick_uniqekey'] = $dataline['pick_uniqekey'];
        //     //     $recordMashup[$index]['created_by'] = $dataline['created_by'];
        //     //     $recordMashup[$index]['modified_by'] = $dataline['modified_by'];
        //     //     $recordMashup[$index]['from_loc'] = $dataline['loc_num'];
        //     //     $recordMashup[$index]['stage_num'] =  $dataline['stage_num'];
        //     //     $recordMashup[$index]['lpn_num'] = $dataline['lpn_num'];
        //     //     $index++;
        //     // }
        //     $recordMashup[$index]['site_id'] = $dataline['site_id'];
        //     $recordMashup[$index]['cust_num'] =  $dataline['cust_num'];
        //     $recordMashup[$index]['co_num'] = $dataline['co_num'];
        //     $recordMashup[$index]['co_line'] = $dataline['co_line'];
        //     $recordMashup[$index]['co_rel'] = $dataline['co_rel'];
        //     $recordMashup[$index]['item_num'] = $dataline['item_num'];
        //     $recordMashup[$index]['qty_ship_co'] = $dataline['qty_ship_co'];
        //     $recordMashup[$index]['qty_ship_lpn'] = $dataline['qty_ship_lpn'];
        //     $recordMashup[$index]['whse_num'] = $dataline['whse_num'];
        //     $recordMashup[$index]['lot_num'] = $dataline['lot_num'];
        //     $recordMashup[$index]['shipment_id'] = 0;
        //     $recordMashup[$index]['base_uom'] = $dataline['uom'];
        //     $recordMashup[$index]['uom'] = $dataline['line_uom'];
        //     $recordMashup[$index]['qty_current'] = $dataline['qty_current'];
        //     $recordMashup[$index]['pick_uniqekey'] = $dataline['pick_uniqekey'];
        //     $recordMashup[$index]['created_by'] = $dataline['created_by'];
        //     $recordMashup[$index]['modified_by'] = $dataline['modified_by'];
        //     $recordMashup[$index]['from_loc'] = $dataline['loc_num'];
        //     $recordMashup[$index]['stage_num'] =  $dataline['stage_num'];
        //     $recordMashup[$index]['lpn_num'] = $dataline['lpn_num'];
        //     $index++;
        //     // }
        // }

        $intIndex = 1;
        $intLpnIndex = 1;
        foreach ($record as $dataline) {
            // dd($dataline);
            //check if contains more than 1 lpn line
            $isKeyNumeric = is_numeric(implode("", array_keys($dataline))); // check if array keys are numeric by combining all keys (because keys not start from 0)
            if (count($dataline) > 1 && $isKeyNumeric) {
                foreach ($dataline as $key => $lpnlineDet) {
                    $recordMashup[$intLpnIndex]['site_id'] = $lpnlineDet['site_id'];
                    $recordMashup[$intLpnIndex]['cust_num'] = $lpnlineDet['cust_num'];
                    $recordMashup[$intLpnIndex]['co_num'] = $lpnlineDet['co_num'];
                    $recordMashup[$intLpnIndex]['co_line'] = $lpnlineDet['co_line'];
                    $recordMashup[$intLpnIndex]['co_rel'] = $lpnlineDet['co_rel'];
                    $recordMashup[$intLpnIndex]['item_num'] = $lpnlineDet['item_num'];
                    $recordMashup[$intLpnIndex]['qty_ship_co'] = $lpnlineDet['qty_ship_co'];
                    $recordMashup[$intLpnIndex]['qty_ship_lpn'] = $lpnlineDet['qty_ship_lpn'];
                    $recordMashup[$intLpnIndex]['whse_num'] = $lpnlineDet['whse_num'];
                    $recordMashup[$intLpnIndex]['lot_num'] = ContainerItem::where('lpn_num', $lpnlineDet['lpn_num'])->where('lpn_line', $lpnlineDet['each_lpn_line'])->value('lot_num') ?? '';
                    $recordMashup[$intLpnIndex]['shipment_id'] = 0;
                    $recordMashup[$intLpnIndex]['base_uom'] = $lpnlineDet['uom'];
                    $recordMashup[$intLpnIndex]['uom'] = $lpnlineDet['line_uom'];
                    $recordMashup[$intLpnIndex]['qty_current'] = $lpnlineDet['qty_current'];
                    $recordMashup[$intLpnIndex]['pick_uniqekey'] = $lpnlineDet['pick_uniqekey'];
                    $recordMashup[$intLpnIndex]['created_by'] = $lpnlineDet['created_by'];
                    $recordMashup[$intLpnIndex]['modified_by'] = $lpnlineDet['modified_by'];
                    $recordMashup[$intLpnIndex]['from_loc'] = $lpnlineDet['loc_num'];
                    $recordMashup[$intLpnIndex]['stage_num'] = $lpnlineDet['stage_num'];
                    $recordMashup[$intLpnIndex]['lpn_num'] = $lpnlineDet['lpn_num'];
                    $recordMashup[$intLpnIndex]['lpn_line'] = $lpnlineDet['each_lpn_line'];
                    $intLpnIndex++;
                }
            } else {
                $getLPNCount = explode(",", $dataline['lpn_line']);
                $getLpnDetailsQTY = ContainerItem::where('lpn_num', $dataline['lpn_num'])->whereIn('lpn_line', $getLPNCount)->orderBy('lpn_line', 'ASC')->get();
                foreach ($getLpnDetailsQTY as $getLpnDetail) {
                    $recordMashup[$intLpnIndex]['site_id'] = $record[$intIndex]['site_id'];
                    $recordMashup[$intLpnIndex]['cust_num'] = $record[$intIndex]['cust_num'];
                    $recordMashup[$intLpnIndex]['co_num'] = $record[$intIndex]['co_num'];
                    $recordMashup[$intLpnIndex]['co_line'] = $record[$intIndex]['co_line'];
                    $recordMashup[$intLpnIndex]['co_rel'] = $record[$intIndex]['co_rel'];
                    $recordMashup[$intLpnIndex]['item_num'] = $record[$intIndex]['item_num'];
                    $recordMashup[$intLpnIndex]['qty_ship_co'] = $record[$intIndex]['qty_ship_co'];
                    $recordMashup[$intLpnIndex]['qty_ship_lpn'] = $record[$intIndex]['qty_ship_lpn'];
                    $recordMashup[$intLpnIndex]['whse_num'] = $record[$intIndex]['whse_num'];
                    $recordMashup[$intLpnIndex]['lot_num'] = $getLpnDetail->lot_num;
                    $recordMashup[$intLpnIndex]['shipment_id'] = 0;
                    $recordMashup[$intLpnIndex]['base_uom'] = $record[$intIndex]['uom'];
                    $recordMashup[$intLpnIndex]['uom'] = $record[$intIndex]['line_uom'];
                    $recordMashup[$intLpnIndex]['qty_current'] = $record[$intIndex]['qty_current'];
                    $recordMashup[$intLpnIndex]['pick_uniqekey'] = $record[$intIndex]['pick_uniqekey'];
                    $recordMashup[$intLpnIndex]['created_by'] = $record[$intIndex]['created_by'];
                    $recordMashup[$intLpnIndex]['modified_by'] = $record[$intIndex]['modified_by'];
                    $recordMashup[$intLpnIndex]['from_loc'] = $record[$intIndex]['loc_num'];
                    $recordMashup[$intLpnIndex]['stage_num'] = $record[$intIndex]['stage_num'];
                    $recordMashup[$intLpnIndex]['lpn_num'] = $getLpnDetail->lpn_num;
                    $recordMashup[$intLpnIndex]['lpn_line'] = $getLpnDetail->lpn_line;

                    $intLpnIndex++;
                }
            }

            // Check LPN Line
            // $getLPNCount = explode(",",$dataline['lpn_line']);
            // $getLpnDetailsQTY = ContainerItem::where('lpn_num', $dataline['lpn_num'])->whereIn('lpn_line', $getLPNCount)->orderBy('lpn_line', 'ASC')->get();
            // foreach($getLpnDetailsQTY as $getLpnDetail){
            //     $recordMashup[$intLpnIndex]['site_id'] = $record[$intIndex]['site_id'];
            //     $recordMashup[$intLpnIndex]['cust_num'] = $record[$intIndex]['cust_num'];
            //     $recordMashup[$intLpnIndex]['co_num'] = $record[$intIndex]['co_num'];
            //     $recordMashup[$intLpnIndex]['co_line'] = $record[$intIndex]['co_line'];
            //     $recordMashup[$intLpnIndex]['co_rel'] = $record[$intIndex]['co_rel'];
            //     $recordMashup[$intLpnIndex]['item_num'] = $record[$intIndex]['item_num'];
            //     $recordMashup[$intLpnIndex]['qty_ship_co'] = $record[$intIndex]['qty_ship_co'];
            //     $recordMashup[$intLpnIndex]['qty_ship_lpn'] = $record[$intIndex]['qty_ship_lpn'];
            //     $recordMashup[$intLpnIndex]['whse_num'] = $record[$intIndex]['whse_num'];
            //     $recordMashup[$intLpnIndex]['lot_num'] = $getLpnDetail->lot_num;
            //     $recordMashup[$intLpnIndex]['shipment_id'] = 0;
            //     $recordMashup[$intLpnIndex]['base_uom'] = $record[$intIndex]['uom'];
            //     $recordMashup[$intLpnIndex]['uom'] = $record[$intIndex]['line_uom'];
            //     $recordMashup[$intLpnIndex]['qty_current'] = $record[$intIndex]['qty_current'];
            //     $recordMashup[$intLpnIndex]['pick_uniqekey'] = $record[$intIndex]['pick_uniqekey'];
            //     $recordMashup[$intLpnIndex]['created_by'] = $record[$intIndex]['created_by'];
            //     $recordMashup[$intLpnIndex]['modified_by'] = $record[$intIndex]['modified_by'];
            //     $recordMashup[$intLpnIndex]['from_loc'] = $record[$intIndex]['loc_num'];
            //     $recordMashup[$intLpnIndex]['stage_num'] = $record[$intIndex]['stage_num'];
            //     $recordMashup[$intLpnIndex]['lpn_num'] = $getLpnDetail->lpn_num;
            //     $recordMashup[$intLpnIndex]['lpn_line'] = $getLpnDetail->lpn_line;

            //     $intLpnIndex++;
            // }
            $intIndex++;
        }
        // dd($record);
        $check = 0;
        $tparm = new TparmView;
        $sap_trans_order_integration = $tparm->getTparmValue('System', 'sap_trans_order_integration');
        $rank = app('App\Http\Controllers\ApiController')->getnextRank(base64_encode($record[1]['whse_num'] ?? $record[1][1]['whse_num']), base64_encode($record[1]['item_num'] ?? $record[1][1]['item_num']), '');
        foreach ($recordMashup as $datas) {
            $transData = new Request($datas);
            $transData['ref_num'] = $transData->co_num;
            $transData['ref_line'] = $transData->co_line;
            $transData['ref_release'] = $transData->co_rel;
            $transData['qty'] = $transData->qty_ship_co;
            $requestTo = clone $transData;
            $datas['qty_ship'] = $transData->qty_ship_co;
            // dd('asd', $datas);
            StagingLine::create($datas);

            // Conversion for CO item
            // Get base uom
            $baseuom = Item::where('item_num', $transData['item_num'])->value('uom');
            $selectuom = $transData['base_uom'];
            $lineuom = $transData['uom'];
            $cust_num = $transData['cust_num'];
            $item_num = $transData['item_num'];
            $qty = $transData['qty_ship_lpn'];
            $form_type = __('mobile.nav.co_picking');

            $convertUom = UomConv::convertUOM($baseuom, $selectuom, $lineuom, $qty, $item_num, $cust_num, '', $form_type);
            // dd($baseuom, $selectuom, $lineuom, $qty, $item_num, $cust_num, '', $form_type, $convertUom);
            $co_qty = $convertUom['conv_qty_to_base']['qty'];
            $uom_conv = $convertUom['conv_qty_to_base']['uom'];
            $co_line_qty = $convertUom['conv_qty_to_line']['qty'];
            $line_uom_conv = $convertUom['conv_qty_to_line']['uom'];
            // $co_qty = UOMService::convertCORequest($transData);

            $fromArr = [
                'whse_num' => $datas['whse_num'],
                'loc_num' => $datas['from_loc'],
                'item_num' =>  $datas['item_num'],
                'qty_conv' => $co_qty,
                'lot_num'  => $datas['lot_num']

            ];


            $updateFromItemLocation = PalletService::updateItemLocLotQty($fromArr, $toArr = [], 0, 'From CO Picking');


            $toArr = [
                'whse_num' => $datas['whse_num'],
                'loc_num'  => $datas['stage_num'],
                'item_num' =>  $datas['item_num'],
                'qty_conv' => $co_qty,
                'lot_num'  => $datas['lot_num'],
                'uom_conv' =>  $uom_conv,
                'rank' =>  $rank

            ];

            $updateToItemLocation = PalletService::updateItemLocLotQty($fromArr = [], $toArr, 0, 'To CO Picking');

            // Update table: coitems
            $coitem = CustomerOrderItem::where('co_num', $transData->co_num)
                ->where('co_line', $transData->co_line)
                ->where('co_rel', $transData->co_rel)
                ->first();
            if ($coitem) {
                $coitem->qty_picked = number_format($coitem->qty_picked + $co_line_qty, 5, '.', '');
                $coitem->qty_manual_picked = number_format($coitem->qty_manual_picked + $co_line_qty, 5, '.', '');
                $coitem->save();
            }

            $requestTo['uom_conv'] = $line_uom_conv;
            $requestTo['qty_conv'] = $co_line_qty;
            $stageLoc = StageLoc::createFromRequest($requestTo);
            $stageLoc['lpn_num'] = $transData->lpn_num;
            $stageLocRecord = $this->pickShipService->updateStageLoc($stageLoc);

            // if($sap_trans_order_integration==1 ){
            //      $result = SapCallService::postCOPicking($transData);
            // }
            $check++;
        }

        $updatePalletStatus = Container::where('lpn_num', $record[1]['lpn_num'] ?? $record[1][1]['lpn_num'])->update(['status' => 'Picked']);
        if ($check == $intLpnIndex) {
            return 'true';
        } else {
            return "false";
        }
    }

    public function CoPalletvalidation($whse_num, $co_num)
    {
        $model = new CustomerOrderItem();
        $result = $model->where('whse_num', $whse_num)->where('co_num', $co_num)->where('rel_status', '!=', 'C')->exists();
        if ($result == true) {
            return "true";
        } else {
            return "false";
        }
    }
}
