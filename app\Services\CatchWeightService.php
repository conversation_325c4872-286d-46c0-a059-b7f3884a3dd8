<?php

namespace App\Services;


use Illuminate\Validation\ValidationException;
use DB;
use App\Item;
use App\ItemLoc;
use App\LotLoc;
use Illuminate\Validation\Rule;
use App\Services\GeneralService;
use App\Services\LotService;
use App\Services\PreassignLotsService;
use App\UomConv;

class CatchWeightService
{
    public static function validateCatchWeightData($request, $tolerance, $tolerance_uom)
    {
        // Validate Item and warehouse
        $validated = $request->validate([
            'item_num' => 'required|exists:items',
            'whse_num' => 'required|exists:warehouses',
            'disableCreateNewItemLoc' => 'required',
        ]);

        $item_num = $request->item_num;
        $item = Item::where('item_num', $item_num)->first();
        $cw_tolerance = ($tolerance * $item->catch_weight_tolerance / 100);

        $whse_num = $request->whse_num;
        $disable_create_new_item_loc = $request->disableCreateNewItemLoc; // If True need to check for lot num exists or not

        if ($disable_create_new_item_loc)
        {
            // Validate Location
            $validated = $request->validate([
                'loc_num' => 'required',
            ]);

            $loc_num = $request->loc_num;
            $is_loc_exists = ItemLoc::where('whse_num', $whse_num)->where('loc_num', $loc_num)->where('item_num', $item_num)->exists();
            if (!$is_loc_exists)
            {
                throw ValidationException::withMessages(['loc_num' => __('error.mobile.loc_not_exists')]);
            }

            // Validate Lots
            if (!$request->incoming)
            {
                foreach ($request->arr_lot_num as $key => $value) {
                    $item_lot_loc = LotLoc::where('whse_num', $whse_num)->where('loc_num', $loc_num)->where('item_num', $item_num)->where('lot_num', $value)->exists();
                    if (!$item_lot_loc)
                    {
                        throw ValidationException::withMessages(['lot_num' => __('error.mobile.does_not_exists', ['resource' => $value])]);
                    }
                }
            }
        }

        // Validate Qty
        $qty = array_sum($request->arr_qty ?? []);

        if ($qty <= 0)
        {
            throw ValidationException::withMessages(['qty' => __('error.admin.qty_must_greater_zero')]);
        }

        // Validate Tolerance
        $total_weight = array_sum($request->arr_qty);
        $minTolerance = $tolerance - $cw_tolerance;
        $maxTolerance = $tolerance + $cw_tolerance;

        if ($total_weight < 0)
        {
            throw ValidationException::withMessages(['total_weight' => __('error.mobile.lessthanqtyreturned', ['resource1' => 'Qty', 'resource2' => '0'])]);
        }

        if ($total_weight >= $minTolerance && $total_weight <= $maxTolerance)
        {
            // Weight is within tolerance - validation passed
        }
        else
        {
            throw ValidationException::withMessages(['total_weight' => __('error.mobile.total_weight_out_of_tolerance')]);
        }
    }

    public static function updateItemLocLotNMatlTrans($request, $tolerance_uom, $transType, $lotType)
    {
        // Lots
        foreach ($request->arr_lot_num as $key => $value) {
            $lot_num = $value;
            $qty = $request->arr_qty[$key];
            $uom = $tolerance_uom;

            $convertUom = UomConv::convertUOM($request->base_uom, $uom, $request->original_uom, $qty, $request->item_num, '', $request->vend_num, __('mobile.nav.po_receipt'));

            $request->merge([
                'qty' => $qty,
                'qty_conv' => $convertUom['conv_qty_to_base']['qty'] ?? 0,
                'lot_num' => $lot_num,
                'uom' => $uom,
                'uom_conv' => $request->base_uom ?? 0,
                'expiry_date' => $request->arr_expiry_date[$key],
                'trans_type' => $transType,
                'transtype' => $transType,
            ]);

            // For CO picking, we need to reduce inventory (negative quantity)
            $inventory_qty = ($lotType == "CO Pick") ? -$request->qty_conv : $request->qty_conv;

            $updateItemLocation = GeneralService::updateItemLocationQty($request->whse_num, $request->loc_num, $request->item_num, $inventory_qty, $request->lot_num, $request->uom_conv);
            $insertmatl = GeneralService::newMatlTrans($transType, $request);

            // Update Lot
            $lotServ =  LotService::updateLot($lotType, $request);

            // Update Preassign Lot
            PreassignLotsService::updatePreassignLot($request->trans_type, $request->ref_num, $request->ref_line, $request->item_num, $request->lot_num, auth()->user()->site_id, $request->qty_conv);
        }
    }
}
