@extends('layout.mobile.app')
@section('content')
@section('title', __('Customer Order Picking - Catch Weight'))

<div class="card-body collapse in">
    <div class="card-block-custom">
        <x-catch-weight-form
            :batch-id="$batch_id"
            :whsenum="$co_item->whse_num"
            :itemnum="$co_item->item_num"
            :itemdesc="$co_item->item_desc"
            :refnum="$co_item->co_num"
            :refline="$co_item->co_line"
            :qtybalance="$co_item->qty_shortage ?? 0"
            :qtybalanceuom="$co_item->uom"
            :submiturl="route('runCoPickCWProcess')"
            :catch-weight-tolerance="$co_item->item->catch_weight_tolerance ?? 0"
            :disable-create-new-item-loc="0"
            :allow-over="$allow_over_pick"
            :line-uom="$co_item->uom"
            :print-label="$printLabel"
            transtype="co"
            trans-type="COPick"
            :incoming="false">

            <!-- Additional CO Pick specific hidden fields -->
            <input type="hidden" name="co_rel" value="{{ $co_item->co_rel }}">
            <input type="hidden" name="cust_num" value="{{ $co_item->cust_num }}">
            <input type="hidden" name="stage_num" value="{{ $stage_num }}">
            <input type="hidden" name="delivery_date" value="{{ request('delivery_date') }}">
            <input type="hidden" name="delivery_trip" value="{{ request('delivery_trip') }}">
            <input type="hidden" name="getCheckNONINV" value="{{ $getCheckNONINV ?? 0 }}">

            <x-slot name="additionalFields">
                <div class="form-group row">
                    <div class="input-group">
                        <p class="col-xs-12 col-md-12 col-lg-12 label-control-custom" style="word-break: break-all; word-wrap: break-word">
                            {{ __('mobile.label.co_num') }}: {{ $co_item->co_num }} | {{ __('mobile.label.co_line') }}: {{ $co_item->co_line }}
                        </p>
                    </div>
                </div>

                <div class="form-group row">
                    <div class="input-group">
                        <p class="col-xs-12 col-md-12 col-lg-12 label-control-custom" style="word-break: break-all; word-wrap: break-word">
                            {{ __('mobile.label.stage_loc') }}: {{ $stage_num }}
                        </p>
                    </div>
                </div>
            </x-slot>

        </x-catch-weight-form>
    </div>
</div>

@endsection
